# 🤖 Jaeger Project - AI Continuation Prompt

## 📋 **CURRENT STATUS: MA<PERSON>OR BREA<PERSON>TH<PERSON>UGH ACHIEVED, CRITICAL ISSUES REMAIN**

### ✅ **MAJOR SUCCESSES COMPLETED:**
1. **SHORT Trade Execution Fixed** - Changed spread from 1.0 to 0.0001 in config.py
2. **Stop Loss/Take Profit Integration Fixed** - Added sl= and tp= parameters to order placement
3. **Backtesting Library Stability** - Added defensive fixes for list.remove() errors
4. **Performance Transformation** - From 0 trades to 1,660+ trades per pattern

### 🚨 **CRITICAL ISSUES REQUIRING IMMEDIATE ATTENTION:**

#### **Priority 1: LLM Take Profit Parsing Failures (60% Pattern Failure Rate)**
- **Problem**: 3 out of 5 patterns fail with parsing errors
- **Error**: `LLM must provide a valid take profit rule. Got: '`High[1] + (High[1] - Low[2]) * 2.5` (Risk-reward ratio of 3:1)'`
- **Root Cause**: Parse<PERSON> cannot handle backticks and risk-reward ratio text in take profit formulas
- **Location**: `_calculate_take_profit` method in `src/llm_rule_parser.py` line 1033
- **Impact**: Patterns 3, 4, and 5 completely non-functional

#### **Priority 2: Massive Order Rejection Rate (96% Signal Loss)**
- **Problem**: 78,721 rejections out of 81,730 signals (96.3% rejection rate)
- **Primary Error**: "SL too close to entry (distance: 0.4000000000014552)"
- **Impact**: Only 2% of signals become actual trades
- **Location**: Order validation logic in `src/cortex.py` `_validate_order_parameters` method

#### **Priority 3: Poor Trading Performance (-55.93% Return)**
- **Problem**: Pattern 2 shows -55.93% return (massive losses)
- **Concern**: Working patterns are generating losses instead of profits
- **Need**: Analysis of why trades are immediately losing money

#### **Priority 4: Signal-to-Order Conversion Optimization**
- **Current**: 2% conversion rate due to overly strict validation
- **Goal**: Improve conversion while maintaining trade safety

## 🛠 **TECHNICAL CONTEXT:**

### **System Architecture:**
- **Main Orchestrator**: `src/cortex.py` - Coordinates LLM → Backtesting → File Generation
- **LLM Integration**: `src/ai_integration/lm_studio_client.py` - Connects to LM Studio
- **Pattern Parsing**: `src/llm_rule_parser.py` - Converts LLM text to executable trading rules
- **Backtesting Engine**: `src/backtesting/` - Modified backtesting.py library
- **Configuration**: `src/config.py` - All system parameters

### **Recent Critical Fixes Applied:**
1. **Spread Fix**: `DEFAULT_SPREAD = 0.0001` (was 1.0) in `src/config.py:89`
2. **Order Parameters**: Added `sl=sl_price, tp=tp_price` in `src/cortex.py:559-562`
3. **Defensive Fixes**: Added existence checks before `list.remove()` in `src/backtesting/_broker.py`

### **Current Working Patterns:**
- **Pattern 1 (LONG)**: `Close[0] > High[1]` - Executes 1,660 trades
- **Pattern 2 (SHORT)**: `Close[0] < Low[1]` - Executes 1,660 trades with -55.93% return

### **Failing Patterns (Parser Issues):**
- **Pattern 3**: Backtick syntax in take profit formula
- **Pattern 4**: Complex risk-reward ratio text
- **Pattern 5**: Multiple formula elements not parsed correctly

## 🎯 **IMMEDIATE NEXT STEPS:**

### **Step 1: Fix Take Profit Parsing**
- Examine `src/llm_rule_parser.py` `_calculate_take_profit` method
- Add support for backtick-wrapped formulas
- Strip risk-reward ratio text from formulas
- Test with failing patterns 3, 4, and 5

### **Step 2: Analyze Order Rejections**
- Review `_validate_order_parameters` in `src/cortex.py`
- Investigate "SL too close to entry" threshold
- Consider relaxing validation rules for realistic market conditions

### **Step 3: Performance Analysis**
- Examine why Pattern 2 generates -55.93% return
- Check if stop losses are too tight or take profits too far
- Analyze actual trade data in results CSV files

## 📁 **KEY FILES TO EXAMINE:**
- `src/llm_rule_parser.py` - Take profit parsing logic
- `src/cortex.py` - Order validation and placement
- `src/config.py` - System configuration
- `results/GBRIDXGBP_20250624_230014/` - Latest test results
- `CHANGELOG.md` - Complete history of fixes

## 🧪 **TESTING APPROACH:**
1. Run `python src/cortex.py` to reproduce current state
2. Check results in `results/` folder for latest performance data
3. Use task management tools to track progress on the 4 remaining issues

## 💡 **SUCCESS CRITERIA:**
- All 5 patterns should parse and execute successfully
- Signal-to-order conversion rate should improve from 2% to >10%
- At least one pattern should show positive returns
- System should maintain stability with high trade volumes

---
**Environment**: `/Users/<USER>/Jaeger` with `llm_env` virtual environment
**LLM**: LM Studio with meta-llama-3.1-8b-instruct model
**Status**: Core execution engine working, optimization phase required
