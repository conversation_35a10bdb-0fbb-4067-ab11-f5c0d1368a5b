#!/usr/bin/env python3
"""
Debug the actual patterns from the latest Jaeger run
"""

import sys
sys.path.append('src')

import pandas as pd
from llm_rule_parser import LLMRuleParser

def test_actual_patterns():
    """Test the actual patterns from the latest run"""
    print("🔍 DEBUGGING ACTUAL PATTERNS FROM LATEST RUN")
    print("=" * 60)
    
    # The actual patterns from the latest run
    pattern_text = """
**PATTERN 1: Bullish Breakout**
Market Logic: When the current price is higher than the previous high, it's a bullish sign.
MT4 Entry: Close[0] > High[1]
MT4 Direction: OP_BUY
MT4 Stop: Low[1]
MT4 Target: Close[0] + (Close[0] - Low[1]) * 2.0
MT4 Position Size: 1 unit
MT4 Timeframe: PERIOD_M5

**PATTERN 2: Bullish Pullback**
Market Logic: When the current price is lower than the previous low, but then bounces back.
MT4 Entry: Close[0] < Low[1] && Close[2] > Close[1]
MT4 Direction: OP_BUY
MT4 Stop: High[1]
MT4 Target: Close[0] + (Close[0] - High[1]) * 1.5
MT4 Position Size: 1 unit
MT4 Timeframe: PERIOD_M5
"""
    
    parser = LLMRuleParser()
    
    print("📋 Parsing actual patterns...")
    try:
        rules = parser.parse_llm_response(pattern_text)
        print(f"✅ Parsed {len(rules)} rules successfully")
        
        for i, rule in enumerate(rules, 1):
            print(f"\n🔍 PATTERN {i}: {rule.entry_condition}")
            print(f"   Direction: {rule.direction}")
            print(f"   Position Size: {rule.position_size}")
            print(f"   Stop Loss: {rule.stop_loss}")
            print(f"   Profit Target: {rule.profit_target}")
            
            # Create rule function
            rule_func = parser._create_python_function(rule)
            
            # Load actual GBRIDXGBP data
            try:
                data_file = "data/2025.6.23GBRIDXGBP_M1_UTCPlus01-M1-No Session.csv"
                print(f"   📊 Loading data from: {data_file}")
                
                data = pd.read_csv(data_file)
                print(f"   📊 Data loaded: {len(data)} bars")
                print(f"   📊 Date range: {data.iloc[0]['DateTime']} to {data.iloc[-1]['DateTime']}")
                print(f"   📊 Price range: {data['Close'].min():.1f} - {data['Close'].max():.1f}")
                
                # Test pattern on actual data
                signals = []
                for idx in range(10, len(data)):  # Start from bar 10 to have enough history
                    signal = rule_func(data, idx)
                    if signal:
                        signals.append((idx, signal))
                        if len(signals) <= 5:  # Show first 5 signals
                            print(f"   📊 Signal {len(signals)} at bar {idx}:")
                            print(f"      Date: {data.iloc[idx]['DateTime']}")
                            print(f"      Price: {data.iloc[idx]['Close']:.1f}")
                            print(f"      Position Size: {signal['position_size']}")
                
                print(f"   🎯 TOTAL SIGNALS: {len(signals)}")
                
                if len(signals) == 0:
                    print(f"   ❌ NO SIGNALS - Pattern too restrictive!")
                    
                    # Debug why no signals
                    print(f"   🔍 Debugging pattern condition...")
                    
                    if rule.entry_condition == "Close[0] > High[1]":
                        # Check how often this condition is met
                        count = 0
                        for idx in range(1, min(100, len(data))):
                            if data.iloc[idx]['Close'] > data.iloc[idx-1]['High']:
                                count += 1
                        print(f"      Close[0] > High[1] met {count}/100 times in first 100 bars")
                        
                    elif "Close[0] < Low[1] && Close[2] > Close[1]" in rule.entry_condition:
                        # Check complex condition
                        count = 0
                        for idx in range(2, min(100, len(data))):
                            if (data.iloc[idx]['Close'] < data.iloc[idx-1]['Low'] and 
                                data.iloc[idx-2]['Close'] > data.iloc[idx-1]['Close']):
                                count += 1
                        print(f"      Complex condition met {count}/100 times in first 100 bars")
                
                else:
                    print(f"   ✅ Pattern generates signals - should work!")
                    
            except FileNotFoundError:
                print(f"   ❌ Data file not found: {data_file}")
            except Exception as e:
                print(f"   ❌ Error loading data: {e}")
                
    except Exception as e:
        print(f"❌ Error parsing patterns: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_actual_patterns()
