#!/usr/bin/env python3
"""
Debug the backtesting parser to see why signals aren't being generated
"""

import sys
import os
sys.path.append('src')

import pandas as pd
from backtesting_rule_parser import BacktestingRuleParser

def create_simple_test_data():
    """Create simple test data that should trigger signals"""
    data = pd.DataFrame({
        'Open': [1.1000, 1.1010, 1.1020, 1.1030, 1.1040],
        'High': [1.1005, 1.1015, 1.1025, 1.1035, 1.1045],
        'Low': [0.9995, 1.1005, 1.1015, 1.1025, 1.1035],
        'Close': [1.1002, 1.1012, 1.1022, 1.1032, 1.1042],
        'Volume': [1000, 1100, 1200, 1300, 1400]
    })
    return data

def test_entry_conditions():
    """Test entry condition evaluation"""
    print("🧪 Testing Entry Condition Evaluation...")
    
    parser = BacktestingRuleParser()
    test_data = create_simple_test_data()
    
    # Test data where current_close > previous_high should trigger
    current = test_data.iloc[2]  # Close = 1.1022
    previous = test_data.iloc[1]  # High = 1.1015
    
    print(f"Current Close: {current['Close']}")
    print(f"Previous High: {previous['High']}")
    print(f"Should trigger (current_close > previous_high): {current['Close'] > previous['High']}")
    
    # Test the parser's evaluation
    result = parser._evaluate_entry_condition('current_close > previous_high', current, previous)
    print(f"Parser result: {result}")
    
    return result

def test_pattern_parsing():
    """Test complete pattern parsing"""
    print("\n🧪 Testing Complete Pattern Parsing...")
    
    sample_pattern = """
**PATTERN 1: Test Pattern**
Market Logic: Simple test pattern
Entry Logic: current_close > previous_high
Direction: long
Stop Logic: previous_low
Target Logic: entry_price + (entry_price - stop_price) * 2.0
Position Size: 1.0
Timeframe: 5min
"""
    
    parser = BacktestingRuleParser()
    try:
        rules = parser.parse_llm_response(sample_pattern)
        print(f"✅ Parsed {len(rules)} rules")
        
        if rules:
            rule = rules[0]
            print(f"Rule details:")
            print(f"  Name: {rule.name}")
            print(f"  Entry Logic: {rule.entry_logic}")
            print(f"  Direction: {rule.direction}")
            print(f"  Stop Logic: {rule.stop_logic}")
            print(f"  Target Logic: {rule.target_logic}")
            
            return rule
        else:
            print("❌ No rules parsed")
            return None
            
    except Exception as e:
        print(f"❌ Parsing failed: {e}")
        return None

def test_function_generation():
    """Test function generation and signal creation"""
    print("\n🧪 Testing Function Generation...")
    
    rule = test_pattern_parsing()
    if not rule:
        return False
    
    parser = BacktestingRuleParser()
    rule_function = parser._create_python_function(rule)
    
    if not rule_function:
        print("❌ No function generated")
        return False
    
    print("✅ Function generated")
    
    # Test with simple data
    test_data = create_simple_test_data()
    
    print("\nTesting function on data:")
    for i in range(1, len(test_data)):
        print(f"\nBar {i}:")
        print(f"  Current: O={test_data.iloc[i]['Open']:.4f} H={test_data.iloc[i]['High']:.4f} L={test_data.iloc[i]['Low']:.4f} C={test_data.iloc[i]['Close']:.4f}")
        print(f"  Previous: O={test_data.iloc[i-1]['Open']:.4f} H={test_data.iloc[i-1]['High']:.4f} L={test_data.iloc[i-1]['Low']:.4f} C={test_data.iloc[i-1]['Close']:.4f}")
        
        signal = rule_function(test_data, i)
        if signal:
            print(f"  🎯 SIGNAL: {signal}")
            return True
        else:
            print(f"  ❌ No signal")
    
    print("❌ No signals generated")
    return False

def test_price_calculations():
    """Test price calculations specifically"""
    print("\n🧪 Testing Price Calculations...")
    
    parser = BacktestingRuleParser()
    
    # Test data
    entry_price = 1.1022
    current = pd.Series({'Close': 1.1022, 'High': 1.1025, 'Low': 1.1015})
    previous = pd.Series({'Close': 1.1012, 'High': 1.1015, 'Low': 1.1005})
    
    # Test stop calculation
    stop_price = parser._calculate_stop_price('previous_low', entry_price, current, previous, 'long')
    print(f"Stop price calculation: {stop_price}")
    
    # Test target calculation
    target_price = parser._calculate_target_price('entry_price + (entry_price - stop_price) * 2.0', entry_price, stop_price, 'long')
    print(f"Target price calculation: {target_price}")
    
    # Validate order
    if stop_price and target_price:
        print(f"Order validation:")
        print(f"  Entry: {entry_price}")
        print(f"  Stop: {stop_price}")
        print(f"  Target: {target_price}")
        print(f"  Stop < Entry: {stop_price < entry_price}")
        print(f"  Target > Entry: {target_price > entry_price}")
        print(f"  Stop distance: {entry_price - stop_price}")
        print(f"  Target distance: {target_price - entry_price}")
        
        min_distance = 0.0005
        valid = (stop_price < entry_price and target_price > entry_price and 
                (entry_price - stop_price) >= min_distance and 
                (target_price - entry_price) >= min_distance)
        print(f"  Valid order: {valid}")
        
        return valid
    
    return False

def run_debug_tests():
    """Run all debug tests"""
    print("🔍 DEBUGGING BACKTESTING PARSER")
    print("=" * 50)
    
    # Test 1: Entry conditions
    entry_test = test_entry_conditions()
    
    # Test 2: Pattern parsing
    pattern_test = test_pattern_parsing() is not None
    
    # Test 3: Function generation
    function_test = test_function_generation()
    
    # Test 4: Price calculations
    price_test = test_price_calculations()
    
    print(f"\n📊 DEBUG RESULTS:")
    print(f"  Entry Condition Evaluation: {'✅' if entry_test else '❌'}")
    print(f"  Pattern Parsing: {'✅' if pattern_test else '❌'}")
    print(f"  Function Generation: {'✅' if function_test else '❌'}")
    print(f"  Price Calculations: {'✅' if price_test else '❌'}")
    
    total_passed = sum([entry_test, pattern_test, function_test, price_test])
    print(f"\n🎯 Overall: {total_passed}/4 tests passed")
    
    if total_passed >= 3:
        print("✅ Parser is mostly working - issue may be in test data or validation")
    else:
        print("❌ Parser has fundamental issues that need fixing")
    
    return total_passed >= 3

if __name__ == "__main__":
    success = run_debug_tests()
    
    if success:
        print("\n💡 RECOMMENDATIONS:")
        print("- Parser core functionality is working")
        print("- Check test data for realistic price movements")
        print("- Verify walk-forward validation thresholds")
        print("- Consider adjusting minimum distance requirements")
    else:
        print("\n💡 RECOMMENDATIONS:")
        print("- Fix parser core issues identified above")
        print("- Review entry condition evaluation logic")
        print("- Check price calculation methods")
