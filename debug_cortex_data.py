#!/usr/bin/env python3
"""
Debug the exact data that Cortex is passing to the backtest
"""

import sys
sys.path.append('src')

import pandas as pd
from data_ingestion import DataIngestionManager
from llm_rule_parser import LLMRuleParser
from backtesting import Backtest, Strategy
from config import <PERSON><PERSON><PERSON><PERSON>onfig

def debug_cortex_data():
    """Debug the exact data flow in Cortex"""
    print("🔍 DEBUGGING CORTEX DATA FLOW")
    print("=" * 50)
    
    config = JaegerConfig()
    
    # Step 1: Load data exactly like Cortex
    data_file = "data/2025.6.23GBRIDXGBP_M1_UTCPlus01-M1-No Session.csv"
    
    try:
        print("📊 Step 1: Loading data like Cortex...")
        ingestion_manager = DataIngestionManager()
        raw_data = ingestion_manager.load_market_data(data_file)
        ohlc_data = ingestion_manager.prepare_for_backtesting(raw_data)
        
        print(f"   ✅ Raw data: {len(raw_data)} records")
        print(f"   ✅ OHLC data: {len(ohlc_data)} records")
        print(f"   📊 OHLC columns: {list(ohlc_data.columns)}")
        print(f"   📊 OHLC shape: {ohlc_data.shape}")
        print(f"   📊 OHLC index type: {type(ohlc_data.index)}")
        
        # Check if data is empty
        if len(ohlc_data) == 0:
            print("❌ OHLC data is empty!")
            return
            
        print(f"   📊 First row: {ohlc_data.iloc[0].to_dict()}")
        print(f"   📊 Last row: {ohlc_data.iloc[-1].to_dict()}")
        
    except Exception as e:
        print(f"❌ Data loading failed: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Step 2: Parse patterns exactly like Cortex
    pattern_text = """
### Pattern 1: Bullish Breakout

**Market Logic:** This pattern works by identifying a bullish breakout where the current close is higher than the previous high, indicating a potential upward trend.

**MT4 Entry:** Close[0] > High[1]
**MT4 Direction:** OP_BUY
**MT4 Stop:** Low[1]
**MT4 Target:** Close[0] + (Close[0] - Low[1]) * 2.0
**MT4 Position Size:** 1 unit
**MT4 Timeframe:** PERIOD_M5
"""
    
    try:
        print("\n📋 Step 2: Parsing patterns like Cortex...")
        parser = LLMRuleParser()
        rules = parser.parse_llm_response(pattern_text)
        rule_functions = []
        for rule in rules:
            rule_func = parser._create_python_function(rule)
            if rule_func:
                rule_functions.append(rule_func)
        
        print(f"   ✅ Parsed {len(rules)} rules")
        print(f"   ✅ Created {len(rule_functions)} rule functions")
        
        if len(rule_functions) == 0:
            print("❌ No rule functions created!")
            return
            
    except Exception as e:
        print(f"❌ Pattern parsing failed: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Step 3: Test rule function directly
    print("\n🔍 Step 3: Testing rule function directly...")
    rule_func = rule_functions[0]
    
    try:
        # Test on a few bars
        signal_count = 0
        for i in range(2, min(20, len(ohlc_data))):
            signal = rule_func(ohlc_data, i)
            if signal:
                signal_count += 1
                print(f"   🎯 Direct test signal {signal_count} at bar {i}: {signal}")
                if signal_count >= 3:
                    break
        
        print(f"   📊 Direct test found {signal_count} signals in first 20 bars")
        
    except Exception as e:
        print(f"❌ Direct rule function test failed: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Step 4: Create the EXACT same strategy as Cortex
    print("\n🔍 Step 4: Creating exact Cortex strategy...")
    
    class PatternStrategy(Strategy):
        def init(self):
            print(f"      🔧 Strategy.init() called")
            self.rule_functions = [rule_func]
            self.full_ohlc_data = ohlc_data.copy()
            self.signal_count = 0
            self.bars_processed = 0
            self.init_called = True
            
            print(f"      📊 Strategy initialized with {len(self.full_ohlc_data)} rows")
            print(f"      📊 Backtest data length: {len(self.data.Close) if hasattr(self, 'data') else 'No data yet'}")
            
        def next(self):
            backtest_bar = len(self.data.Close) - 1
            current_idx = backtest_bar
            self.bars_processed += 1

            # Only show first 10 bars for debugging
            show_debug = self.bars_processed <= 10

            if show_debug:
                print(f"      🔧 Strategy.next() called - bar {self.bars_processed}")

            if current_idx < 2:
                if show_debug:
                    print(f"      ⏭️ Skipping bar {current_idx} (need at least 2 bars)")
                return

            try:
                signal = rule_func(self.full_ohlc_data, current_idx)

                if signal:
                    self.signal_count += 1
                    print(f"      🎯 SIGNAL {self.signal_count} at bar {current_idx}: {signal}")

                    # NOW TEST ORDER PLACEMENT LIKE CORTEX DOES
                    position_size = signal.get('position_size', 1.0)
                    direction = signal.get('direction', 'long')
                    entry_price = signal.get('entry_price')
                    sl_price = signal.get('stop_loss')
                    tp_price = signal.get('take_profit')

                    print(f"      🔍 Validating order: direction={direction}, entry={entry_price}, sl={sl_price}, tp={tp_price}")

                    # Test validation like Cortex does
                    if self._validate_order_parameters(direction, entry_price, sl_price, tp_price, current_idx):
                        try:
                            print(f"      📊 Attempting to place {direction.upper()} order with size {position_size}")
                            if direction == 'long':
                                order = self.buy(size=position_size)
                            else:
                                order = self.sell(size=position_size)

                            if order:
                                print(f"      ✅ Order placed successfully: {order}")
                            else:
                                print(f"      ❌ Order returned None")
                        except Exception as e:
                            print(f"      ❌ Order placement failed: {e}")
                    else:
                        print(f"      ❌ Order validation failed")

                elif show_debug:
                    print(f"      ❌ No signal at bar {current_idx}")

            except Exception as e:
                print(f"      ❌ Rule function error at bar {current_idx}: {e}")
                import traceback
                traceback.print_exc()

        def _validate_order_parameters(self, direction, entry_price, sl_price, tp_price, bar_idx):
            """EXACT same validation as Cortex"""
            if not all([entry_price, sl_price, tp_price]):
                print(f"         ❌ Missing parameters: entry={entry_price}, sl={sl_price}, tp={tp_price}")
                return False

            # Smart direction detection like Cortex
            actual_direction = 'long' if sl_price < entry_price else 'short'

            if direction != actual_direction:
                print(f"         🔧 Direction mismatch: stated={direction}, actual={actual_direction}")
                direction = actual_direction

            # Validate relationships
            if direction == 'long':
                if sl_price >= entry_price:
                    print(f"         ❌ LONG: SL ({sl_price:.4f}) >= entry ({entry_price:.4f})")
                    return False
                if tp_price <= entry_price:
                    print(f"         ❌ LONG: TP ({tp_price:.4f}) <= entry ({entry_price:.4f})")
                    return False

                min_distance = 0.5
                if (entry_price - sl_price) < min_distance:
                    print(f"         ❌ LONG: SL too close (distance: {entry_price - sl_price:.4f})")
                    return False
                if (tp_price - entry_price) < min_distance:
                    print(f"         ❌ LONG: TP too close (distance: {tp_price - entry_price:.4f})")
                    return False

            print(f"         ✅ Validation passed for {direction.upper()}")
            return True
    
    # Step 5: Run the backtest exactly like Cortex
    print("\n🔍 Step 5: Running backtest exactly like Cortex...")
    
    try:
        print(f"   📊 Creating backtest with {len(ohlc_data)} rows...")
        print(f"   📊 Data range: {ohlc_data.index.min()} to {ohlc_data.index.max()}")
        
        bt = Backtest(
            ohlc_data,
            PatternStrategy,
            cash=config.DEFAULT_INITIAL_CASH,
            spread=config.DEFAULT_SPREAD,
            commission=config.DEFAULT_COMMISSION,
            margin=config.DEFAULT_MARGIN,
            exclusive_orders=True
        )
        
        print(f"   🔄 Running backtest...")
        stats = bt.run()
        
        print(f"   ✅ Backtest completed!")
        print(f"   📊 Strategy bars processed: {stats._strategy.bars_processed}")
        print(f"   📊 Strategy signals: {stats._strategy.signal_count}")
        print(f"   📊 Trades executed: {stats['# Trades']}")
        print(f"   📈 Return: {stats['Return [%]']:.2f}%")
        
        # Analyze the results
        if stats._strategy.bars_processed == 0:
            print("❌ Strategy.next() was never called!")
        elif stats._strategy.signal_count == 0:
            print("❌ No signals generated during backtest")
        elif stats['# Trades'] == 0:
            print("❌ Signals generated but no trades executed")
        else:
            print("✅ Everything working correctly!")
            
    except Exception as e:
        print(f"❌ Backtest failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_cortex_data()
