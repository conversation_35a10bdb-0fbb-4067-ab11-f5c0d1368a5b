#!/usr/bin/env python3
"""
Debug the EXACT same execution path as Cortex
"""

import sys
sys.path.append('src')

import pandas as pd
from data_ingestion import DataIngestionManager
from llm_rule_parser import LLMRuleParser
from backtesting import Backtest, Strategy
from config import <PERSON><PERSON><PERSON><PERSON>onfig

def debug_cortex_exact():
    """Debug the EXACT same execution path as Cortex"""
    print("🔍 DEBUGGING EXACT CORTEX EXECUTION PATH")
    print("=" * 60)
    
    config = JaegerConfig()
    
    # Step 1: Load data EXACTLY like Cortex
    data_file = "data/2025.6.23GBRIDXGBP_M1_UTCPlus01-M1-No Session.csv"
    
    try:
        print("📊 Loading data exactly like Cortex...")
        ingestion_manager = DataIngestionManager()
        raw_data = ingestion_manager.load_market_data(data_file)
        ohlc_data = ingestion_manager.prepare_for_backtesting(raw_data)
        
        print(f"   ✅ Data loaded: {len(ohlc_data)} records")
        print(f"   📊 Data shape: {ohlc_data.shape}")
        print(f"   📊 Data columns: {list(ohlc_data.columns)}")
        print(f"   📊 Data index type: {type(ohlc_data.index)}")
        
    except Exception as e:
        print(f"❌ Data loading failed: {e}")
        return
    
    # Step 2: Parse patterns EXACTLY like Cortex
    pattern_text = """
### Pattern 1: Bullish Breakout

**Market Logic:** This pattern works by identifying a bullish breakout where the current close is higher than the previous high, indicating a potential upward trend.

**MT4 Entry:** Close[0] > High[1]
**MT4 Direction:** OP_BUY
**MT4 Stop:** Low[1]
**MT4 Target:** Close[0] + (Close[0] - Low[1]) * 2.0
**MT4 Position Size:** 1 unit
**MT4 Timeframe:** PERIOD_M5
"""
    
    try:
        print("\n📋 Parsing patterns exactly like Cortex...")
        parser = LLMRuleParser()
        rules = parser.parse_llm_response(pattern_text)
        
        rule_functions = []
        individual_patterns = [pattern_text]
        
        for rule in rules:
            rule_func = parser._create_python_function(rule)
            if rule_func:
                rule_functions.append(rule_func)
        
        print(f"   ✅ Created {len(rule_functions)} rule functions")
        
    except Exception as e:
        print(f"❌ Pattern parsing failed: {e}")
        return
    
    # Step 3: Create EXACT same strategy as Cortex
    print("\n🔍 Creating EXACT same PatternStrategy as Cortex...")
    
    rule_func = rule_functions[0]
    
    # This is the EXACT same class definition as in Cortex
    class PatternStrategy(Strategy):
        def init(self):
            self.rule_functions = [rule_func]
            # CRITICAL FIX: Store full dataset for rule evaluation
            # The backtesting framework truncates self.data during next() calls
            # but our rule functions need access to full historical data
            self.full_ohlc_data = ohlc_data.copy()
            # Store pattern text for risk analysis
            self.pattern_text = pattern_text
            # Initialize counters for diagnostics
            self.signal_count = 0
            self.order_count = 0
            self._order_rejection_count = 0
            self._validation_failure_count = 0
            self.bars_processed = 0
            
            print(f"      🔧 PatternStrategy.init() called")
            print(f"      📊 Full OHLC data: {len(self.full_ohlc_data)} rows")

        def next(self):
            # CRITICAL FIX: Calculate correct index in full dataset
            # The backtesting framework calls next() for each bar sequentially
            # but len(self.data.Close) gives us the current bar position in the backtest
            # We need to map this to the correct index in the full dataset
            backtest_bar = len(self.data.Close) - 1

            # CRITICAL: The full dataset index should match the backtest progression
            # Since we're using the same dataset for both, the index should be the same
            current_idx = backtest_bar

            if current_idx < 2:
                return

            # Count bars processed for diagnostics
            self.bars_processed += 1

            # CRITICAL FIX: Use stored full dataset instead of truncated self.data
            # Rule functions expect full historical data to access previous bars
            try:
                # DEBUG: Add detailed logging to trace the issue
                if current_idx % 1000 == 0 or current_idx < 10:  # Log first 10 bars and every 1000 bars
                    print(f"      🔍 Testing bar {current_idx}/{len(self.full_ohlc_data)} (processed: {self.bars_processed})")
                    print(f"         Backtest data length: {len(self.data.Close)}")
                    print(f"         Full data length: {len(self.full_ohlc_data)}")
                    print(f"         Current Close: {self.data.Close[-1]:.1f}")
                    print(f"         Full data Close[{current_idx}]: {self.full_ohlc_data.iloc[current_idx]['Close']:.1f}")
                    
                    # Check if data matches
                    if abs(self.data.Close[-1] - self.full_ohlc_data.iloc[current_idx]['Close']) > 0.01:
                        print(f"         ❌ DATA MISMATCH DETECTED!")
                        print(f"            Backtest Close: {self.data.Close[-1]:.1f}")
                        print(f"            Full data Close: {self.full_ohlc_data.iloc[current_idx]['Close']:.1f}")

                signal = rule_func(self.full_ohlc_data, current_idx)

                # DEBUG: Log signal results
                if signal:
                    print(f"      🎯 CORTEX SIGNAL {self.signal_count + 1} at bar {current_idx}: {signal}")
                    self.signal_count += 1

                    # Position size is already set correctly by rule parser
                    position_size = signal.get('position_size', 1.0)  # Use 1 absolute unit as fallback

                    # Validate stop loss and take profit values
                    sl_price = signal.get('stop_loss')
                    tp_price = signal.get('take_profit')
                    entry_price = signal.get('entry_price')
                    direction = signal.get('direction', 'long')

                    # EXACT same validation as Cortex
                    if self._validate_order_parameters(direction, entry_price, sl_price, tp_price, current_idx):
                        try:
                            print(f"      📊 Placing {direction.upper()} order with size {position_size}")
                            
                            if direction == 'long':
                                order = self.buy(size=position_size)
                            else:
                                order = self.sell(size=position_size)
                                
                            if order:
                                self.order_count += 1
                                print(f"      ✅ Order {self.order_count} placed successfully!")
                            else:
                                self._order_rejection_count += 1
                                print(f"      ❌ Order returned None (rejection #{self._order_rejection_count})")
                                
                        except Exception as e:
                            self._order_rejection_count += 1
                            print(f"      ❌ Order placement failed: {e}")
                    else:
                        self._validation_failure_count += 1
                        print(f"      ❌ Order validation failed (failure #{self._validation_failure_count})")

            except Exception as e:
                print(f"      ❌ Rule function error at bar {current_idx}: {e}")
                import traceback
                traceback.print_exc()

        def _validate_order_parameters(self, direction, entry_price, sl_price, tp_price, bar_idx):
            """EXACT same validation as Cortex"""
            if not all([entry_price, sl_price, tp_price]):
                print(f"         ❌ Missing order parameters at bar {bar_idx}: entry={entry_price}, sl={sl_price}, tp={tp_price}")
                return False

            # SMART DIRECTION DETECTION: Auto-detect actual direction from stop loss position
            actual_direction = 'long' if sl_price < entry_price else 'short'
            
            if direction != actual_direction:
                print(f"         🔧 PATTERN LOGIC MISMATCH at bar {bar_idx}:")
                print(f"            Stated: {direction.upper()}, Actual: {actual_direction.upper()}")
                print(f"            Entry: {entry_price:.4f}, Stop: {sl_price:.4f}")
                print(f"         🔧 Using actual direction: {actual_direction.upper()}")
                direction = actual_direction

            # Validate LONG order relationships
            if direction == 'long':
                if sl_price >= entry_price:
                    print(f"         ❌ LONG: Stop loss ({sl_price:.4f}) >= entry ({entry_price:.4f})")
                    return False
                if tp_price <= entry_price:
                    print(f"         ❌ LONG: Take profit ({tp_price:.4f}) <= entry ({entry_price:.4f})")
                    return False
                    
                min_distance = 0.5
                if (entry_price - sl_price) < min_distance:
                    print(f"         ❌ LONG: Stop too close (distance: {entry_price - sl_price:.4f})")
                    return False
                if (tp_price - entry_price) < min_distance:
                    print(f"         ❌ LONG: Target too close (distance: {tp_price - entry_price:.4f})")
                    return False
            else:  # short
                if sl_price <= entry_price:
                    print(f"         ❌ SHORT: Stop loss ({sl_price:.4f}) <= entry ({entry_price:.4f})")
                    return False
                if tp_price >= entry_price:
                    print(f"         ❌ SHORT: Take profit ({tp_price:.4f}) >= entry ({entry_price:.4f})")
                    return False
                    
                min_distance = 0.5
                if (sl_price - entry_price) < min_distance:
                    print(f"         ❌ SHORT: Stop too close (distance: {sl_price - entry_price:.4f})")
                    return False
                if (entry_price - tp_price) < min_distance:
                    print(f"         ❌ SHORT: Target too close (distance: {entry_price - tp_price:.4f})")
                    return False

            print(f"         ✅ {direction.upper()} pattern validation passed")
            return True
    
    # Step 4: Run EXACT same backtest as Cortex
    print("\n🔄 Running EXACT same backtest as Cortex...")
    
    try:
        bt = Backtest(
            ohlc_data,
            PatternStrategy,
            cash=config.DEFAULT_INITIAL_CASH,
            spread=config.DEFAULT_SPREAD,
            commission=config.DEFAULT_COMMISSION,
            margin=config.DEFAULT_MARGIN,
            exclusive_orders=True
        )
        
        print(f"   📊 Backtest configuration:")
        print(f"      Cash: ${config.DEFAULT_INITIAL_CASH:,.0f}")
        print(f"      Margin: {config.DEFAULT_MARGIN}")
        print(f"      Spread: {config.DEFAULT_SPREAD}")
        print(f"      Commission: {config.DEFAULT_COMMISSION}")
        
        stats = bt.run()
        strategy = stats._strategy
        
        print(f"\n📊 EXACT CORTEX RESULTS:")
        print(f"   Bars processed: {strategy.bars_processed}")
        print(f"   Signals generated: {strategy.signal_count}")
        print(f"   Orders placed: {strategy.order_count}")
        print(f"   Order rejections: {strategy._order_rejection_count}")
        print(f"   Validation failures: {strategy._validation_failure_count}")
        print(f"   Trades executed: {stats['# Trades']}")
        print(f"   Return: {stats['Return [%]']:.2f}%")
        
        # Analyze the results
        if strategy.bars_processed == 0:
            print("\n❌ CRITICAL ISSUE: Strategy.next() was never called!")
            print("   This means the backtest didn't run at all")
        elif strategy.signal_count == 0:
            print("\n❌ CRITICAL ISSUE: No signals generated")
            print("   This means the rule function is not working in Cortex context")
        elif strategy.order_count == 0:
            print("\n❌ ISSUE: Signals generated but no orders placed")
            print("   This means all orders failed validation")
        elif stats['# Trades'] == 0:
            print("\n❌ ISSUE: Orders placed but no trades executed")
            print("   This means orders were rejected by backtesting engine")
        else:
            print("\n✅ SUCCESS: Cortex execution working correctly!")
            
    except Exception as e:
        print(f"❌ Backtest failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_cortex_exact()
