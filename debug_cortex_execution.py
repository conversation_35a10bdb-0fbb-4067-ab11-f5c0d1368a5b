#!/usr/bin/env python3
"""
Debug the exact execution path that Cortex uses
"""

import sys
sys.path.append('src')

import pandas as pd
import numpy as np
from data_ingestion import DataIngestionManager
from llm_rule_parser import LLMRuleParser
from backtesting import Backtest, Strategy
from config import <PERSON><PERSON><PERSON><PERSON>onfig

def debug_cortex_execution():
    """Debug the exact same execution path that Cortex uses"""
    print("🔍 DEBUGGING CORTEX EXECUTION PATH")
    print("=" * 60)
    
    config = JaegerConfig()
    
    # Use the exact same data file that <PERSON><PERSON><PERSON> uses
    data_file = "data/2025.6.23GBRIDXGBP_M1_UTCPlus01-M1-No Session.csv"
    
    print(f"📊 Loading data exactly like Cortex...")
    
    # Load data exactly like Cortex does
    try:
        ingestion_manager = DataIngestionManager()
        raw_data = ingestion_manager.load_market_data(data_file)
        ohlc_data = ingestion_manager.prepare_for_backtesting(raw_data)
        print(f"✅ Data loaded: {len(ohlc_data)} records")
        print(f"   Date range: {ohlc_data.index.min()} to {ohlc_data.index.max()}")
        print(f"   Columns: {list(ohlc_data.columns)}")
        print(f"   Data types: {ohlc_data.dtypes.to_dict()}")
        print(f"   First few rows:")
        print(ohlc_data.head(3))
    except Exception as e:
        print(f"❌ Data loading failed: {e}")
        return
    
    # Use the exact patterns from the latest Jaeger run
    pattern_text = """
### Pattern 1: Bullish Breakout

**Market Logic:** This pattern works by identifying a bullish breakout where the current close is higher than the previous high, indicating a potential upward trend.

**MT4 Entry:** Close[0] > High[1]
**MT4 Direction:** OP_BUY
**MT4 Stop:** Low[1]
**MT4 Target:** Close[0] + (Close[0] - Low[1]) * 2.0
**MT4 Position Size:** 1 unit
**MT4 Timeframe:** PERIOD_M5
"""
    
    print(f"📋 Parsing patterns exactly like Cortex...")
    
    # Parse patterns exactly like Cortex does
    try:
        parser = LLMRuleParser()
        rules = parser.parse_llm_response(pattern_text)
        print(f"✅ Parsed {len(rules)} rules")
        
        # Create rule functions exactly like Cortex does
        rule_functions = []
        for rule in rules:
            rule_func = parser._create_python_function(rule)
            if rule_func:
                rule_functions.append(rule_func)
        
        print(f"✅ Created {len(rule_functions)} rule functions")
        
    except Exception as e:
        print(f"❌ Pattern parsing failed: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Now test the EXACT same execution path as Cortex
    print(f"\n🔍 TESTING EXACT CORTEX EXECUTION PATH...")
    
    rule_func = rule_functions[0]  # Test Pattern 1
    
    # Create the EXACT same strategy class that Cortex uses
    class PatternStrategy(Strategy):
        def init(self):
            self.rule_functions = [rule_func]
            self.full_ohlc_data = ohlc_data.copy()  # EXACT same as Cortex
            self.signal_count = 0
            self.bars_processed = 0
            
        def next(self):
            current_idx = len(self.data.Close) - 1
            self.bars_processed += 1
            
            if current_idx < 2:
                return
                
            try:
                # EXACT same call as Cortex line 505
                signal = rule_func(self.full_ohlc_data, current_idx)
                
                # EXACT same logging as Cortex
                if current_idx % 100 == 0:
                    print(f"      🔍 Testing bar {current_idx}/{len(self.full_ohlc_data)} (processed: {self.bars_processed})")
                
                if signal:
                    print(f"      🎯 CORTEX SIGNAL {self.signal_count + 1} at bar {current_idx}: {signal}")
                    self.signal_count += 1
                    
                    # EXACT same position sizing as Cortex
                    position_size = signal.get('position_size', 1.0)
                    
                    # EXACT same validation as Cortex
                    direction = signal.get('direction', 'long')
                    entry_price = signal.get('entry_price')
                    sl_price = signal.get('stop_loss')
                    tp_price = signal.get('take_profit')
                    
                    if self._validate_order_parameters(direction, entry_price, sl_price, tp_price, current_idx):
                        try:
                            if direction == 'long':
                                order = self.buy(size=position_size)
                            else:
                                order = self.sell(size=position_size)
                                
                            if order:
                                print(f"      ✅ Order placed successfully!")
                            else:
                                print(f"      ❌ Order returned None")
                        except Exception as e:
                            print(f"      ❌ Order placement failed: {e}")
                    else:
                        print(f"      ❌ Order validation failed")
                        
            except Exception as e:
                print(f"      ❌ Rule function error at bar {current_idx}: {e}")
                import traceback
                traceback.print_exc()
        
        def _validate_order_parameters(self, direction, entry_price, sl_price, tp_price, bar_idx):
            """EXACT same validation as Cortex"""
            if not all([entry_price, sl_price, tp_price]):
                print(f"      ❌ Missing order parameters at bar {bar_idx}: entry={entry_price}, sl={sl_price}, tp={tp_price}")
                return False

            # EXACT same validation logic as Cortex
            if direction == 'long':
                if sl_price >= entry_price:
                    print(f"      ❌ LONG: Stop loss ({sl_price:.4f}) >= entry ({entry_price:.4f})")
                    return False
                if tp_price <= entry_price:
                    print(f"      ❌ LONG: Take profit ({tp_price:.4f}) <= entry ({entry_price:.4f})")
                    return False
                    
                min_distance = 0.5
                if (entry_price - sl_price) < min_distance:
                    print(f"      ❌ LONG: Stop too close (distance: {entry_price - sl_price:.4f})")
                    return False
                if (tp_price - entry_price) < min_distance:
                    print(f"      ❌ LONG: Target too close (distance: {tp_price - entry_price:.4f})")
                    return False
            
            return True
    
    # Run the EXACT same backtest as Cortex
    print(f"\n🔄 Running EXACT same backtest as Cortex...")
    
    try:
        bt = Backtest(
            ohlc_data,
            PatternStrategy,
            cash=config.DEFAULT_INITIAL_CASH,
            spread=config.DEFAULT_SPREAD,
            commission=config.DEFAULT_COMMISSION,
            margin=config.DEFAULT_MARGIN,
            exclusive_orders=True
        )
        
        stats = bt.run()
        strategy = stats._strategy
        
        print(f"\n🎯 CORTEX EXECUTION RESULTS:")
        print(f"   📊 Bars processed: {strategy.bars_processed}")
        print(f"   📊 Signals generated: {strategy.signal_count}")
        print(f"   📊 Trades executed: {stats['# Trades']}")
        print(f"   📈 Return: {stats['Return [%]']:.2f}%")
        
        if strategy.signal_count == 0:
            print(f"\n❌ NO SIGNALS GENERATED - This matches the Jaeger issue!")
            print(f"   Let's debug why the rule function isn't generating signals...")
            
            # Test the rule function directly on a few bars
            print(f"\n🔍 DIRECT RULE FUNCTION TEST:")
            for test_idx in range(2, min(10, len(ohlc_data))):
                try:
                    signal = rule_func(ohlc_data, test_idx)
                    if signal:
                        print(f"   ✅ Signal at bar {test_idx}: {signal}")
                        break
                    else:
                        current_close = ohlc_data.iloc[test_idx]['Close']
                        prev_high = ohlc_data.iloc[test_idx-1]['High']
                        print(f"   ❌ No signal at bar {test_idx}: Close[0]={current_close:.1f} vs High[1]={prev_high:.1f}")
                except Exception as e:
                    print(f"   ❌ Error at bar {test_idx}: {e}")
        else:
            print(f"   ✅ Signals generated successfully!")
            
    except Exception as e:
        print(f"❌ Backtest failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_cortex_execution()
