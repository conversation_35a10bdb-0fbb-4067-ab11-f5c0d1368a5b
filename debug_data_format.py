#!/usr/bin/env python3
"""
Debug the data format issue
"""

import sys
sys.path.append('src')

import pandas as pd
from data_ingestion import DataIngestionManager

def debug_data_format():
    """Debug the data format that's causing issues"""
    print("🔍 DEBUGGING DATA FORMAT")
    print("=" * 40)
    
    # Load data exactly like Cortex does
    data_file = "data/2025.6.23GBRIDXGBP_M1_UTCPlus01-M1-No Session.csv"
    
    try:
        ingestion_manager = DataIngestionManager()
        raw_data = ingestion_manager.load_market_data(data_file)
        ohlc_data = ingestion_manager.prepare_for_backtesting(raw_data)
        
        print(f"✅ Data loaded: {len(ohlc_data)} records")
        print(f"📊 Columns: {list(ohlc_data.columns)}")
        print(f"📊 Data types: {ohlc_data.dtypes.to_dict()}")
        print(f"📊 Index type: {type(ohlc_data.index)}")
        print(f"📊 Index name: {ohlc_data.index.name}")
        print(f"📊 Has NaN values: {ohlc_data.isnull().any().any()}")
        print(f"📊 Shape: {ohlc_data.shape}")
        
        print(f"\n📊 First 5 rows:")
        print(ohlc_data.head())
        
        print(f"\n📊 Last 5 rows:")
        print(ohlc_data.tail())
        
        print(f"\n📊 Sample data statistics:")
        print(ohlc_data.describe())
        
        # Check if the data is suitable for backtesting
        required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
        missing_columns = [col for col in required_columns if col not in ohlc_data.columns]
        
        if missing_columns:
            print(f"❌ Missing required columns: {missing_columns}")
        else:
            print(f"✅ All required columns present")
        
        # Check for data quality issues
        print(f"\n🔍 DATA QUALITY CHECKS:")
        
        # Check for invalid OHLC relationships
        invalid_ohlc = (
            (ohlc_data['High'] < ohlc_data['Low']) |
            (ohlc_data['High'] < ohlc_data['Open']) |
            (ohlc_data['High'] < ohlc_data['Close']) |
            (ohlc_data['Low'] > ohlc_data['Open']) |
            (ohlc_data['Low'] > ohlc_data['Close'])
        )
        
        if invalid_ohlc.any():
            print(f"❌ Found {invalid_ohlc.sum()} rows with invalid OHLC relationships")
            print(f"   First few invalid rows:")
            print(ohlc_data[invalid_ohlc].head())
        else:
            print(f"✅ All OHLC relationships are valid")
        
        # Check for zero or negative prices
        zero_prices = (
            (ohlc_data['Open'] <= 0) |
            (ohlc_data['High'] <= 0) |
            (ohlc_data['Low'] <= 0) |
            (ohlc_data['Close'] <= 0)
        )
        
        if zero_prices.any():
            print(f"❌ Found {zero_prices.sum()} rows with zero or negative prices")
        else:
            print(f"✅ All prices are positive")
        
        # Check for duplicate timestamps
        duplicate_times = ohlc_data.index.duplicated()
        if duplicate_times.any():
            print(f"❌ Found {duplicate_times.sum()} duplicate timestamps")
        else:
            print(f"✅ No duplicate timestamps")
        
        # Test a simple backtesting operation
        print(f"\n🔍 TESTING SIMPLE BACKTEST:")
        
        try:
            from backtesting import Backtest, Strategy
            
            class TestStrategy(Strategy):
                def init(self):
                    self.test_count = 0
                    
                def next(self):
                    self.test_count += 1
                    if self.test_count <= 5:
                        print(f"   📊 Test strategy called for bar {self.test_count}")
                        print(f"      Current data length: {len(self.data.Close)}")
                        print(f"      Current Close: {self.data.Close[-1]:.1f}")
                        print(f"      Current High: {self.data.High[-1]:.1f}")
                        print(f"      Current Low: {self.data.Low[-1]:.1f}")
            
            # Test with a small subset first
            test_data = ohlc_data.head(100)
            print(f"   Testing with {len(test_data)} rows...")
            
            bt = Backtest(test_data, TestStrategy, cash=100000)
            stats = bt.run()
            
            print(f"   ✅ Backtest completed successfully!")
            print(f"   📊 Strategy was called {stats._strategy.test_count} times")
            
        except Exception as e:
            print(f"   ❌ Backtest failed: {e}")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"❌ Data loading failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_data_format()
