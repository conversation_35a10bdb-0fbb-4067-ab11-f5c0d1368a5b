#!/usr/bin/env python3
"""
Debug using the EXACT same data and patterns that <PERSON><PERSON><PERSON> uses
"""

import sys
sys.path.append('src')

import pandas as pd
from data_ingestion import DataIngestionManager
from llm_rule_parser import LLMRuleParser
from backtesting import Backtest, Strategy
from config import <PERSON><PERSON>gerConfig

def debug_exact_jaeger_flow():
    """Debug using exact Jaeger data and patterns"""
    print("🔍 EXACT JAEGER FLOW DEBUG")
    print("=" * 50)
    
    config = JaegerConfig()
    
    # Use the exact same data file that <PERSON><PERSON><PERSON> uses
    data_file = "data/2025.6.23GBRIDXGBP_M1_UTCPlus01-M1-No Session.csv"
    
    print(f"📊 Loading data from: {data_file}")
    
    # Load data exactly like Cortex does
    try:
        ingestion_manager = DataIngestionManager()
        raw_data = ingestion_manager.load_market_data(data_file)
        ohlc_data = ingestion_manager.prepare_for_backtesting(raw_data)
        print(f"✅ Data loaded: {len(ohlc_data)} records")
        print(f"   Date range: {ohlc_data.index.min()} to {ohlc_data.index.max()}")
        print(f"   Price range: {ohlc_data['Close'].min():.1f} - {ohlc_data['Close'].max():.1f}")
    except Exception as e:
        print(f"❌ Data loading failed: {e}")
        return
    
    # Use the exact patterns from the latest Jaeger run
    pattern_text = """
**Pattern 1: Bullish Breakout**
Market Logic: This pattern works because when the current close price is higher than the previous high, it indicates a bullish breakout.
MT4 Entry: Close[0] > High[1]
MT4 Direction: OP_BUY
MT4 Stop: Low[1]
MT4 Target: Close[0] + (Close[0] - Low[1]) * 2.0
MT4 Position Size: 1 unit
MT4 Timeframe: PERIOD_M5

**Pattern 2: Bullish Pullback**
Market Logic: This pattern works because when the current close price is lower than the previous low, it indicates a pullback.
MT4 Entry: Close[0] < Low[1]
MT4 Direction: OP_BUY
MT4 Stop: High[1]
MT4 Target: Close[0] - (High[1] - Close[0]) * 1.5
MT4 Position Size: 1 unit
MT4 Timeframe: PERIOD_M5
"""
    
    print(f"📋 Parsing patterns exactly like Cortex does...")
    
    # Parse patterns exactly like Cortex does
    try:
        parser = LLMRuleParser()
        rules = parser.parse_llm_response(pattern_text)
        print(f"✅ Parsed {len(rules)} rules")
        
        # Create rule functions exactly like Cortex does
        rule_functions = []
        for rule in rules:
            rule_func = parser._create_python_function(rule)
            if rule_func:
                rule_functions.append(rule_func)
        
        print(f"✅ Created {len(rule_functions)} rule functions")
        
    except Exception as e:
        print(f"❌ Pattern parsing failed: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Test each pattern individually like Cortex does
    for i, rule_func in enumerate(rule_functions, 1):
        print(f"\n🔍 Testing Pattern {i} with exact Jaeger data...")
        
        # Create strategy exactly like Cortex does
        class JaegerPatternStrategy(Strategy):
            def init(self):
                self.rule_functions = [rule_func]
                self.full_ohlc_data = ohlc_data.copy()
                self.signal_count = 0
                self.order_count = 0
                self.validation_failures = 0
                self.order_rejections = 0
                
            def next(self):
                current_idx = len(self.data.Close) - 1
                if current_idx < 2:
                    return
                    
                try:
                    # Use the exact same logic as Cortex
                    signal = rule_func(self.full_ohlc_data, current_idx)
                    
                    if signal:
                        self.signal_count += 1
                        
                        # Log first few signals for debugging
                        if self.signal_count <= 3:
                            print(f"      🎯 Signal {self.signal_count} at bar {current_idx}:")
                            print(f"         Entry: {signal.get('entry_price', 'N/A')}")
                            print(f"         Stop: {signal.get('stop_loss', 'N/A')}")
                            print(f"         Target: {signal.get('take_profit', 'N/A')}")
                            print(f"         Size: {signal.get('position_size', 'N/A')}")
                        
                        # Validate exactly like Cortex does
                        if self._validate_signal(signal):
                            try:
                                position_size = signal.get('position_size', 1.0)
                                if signal['direction'] == 'long':
                                    order = self.buy(size=position_size)
                                else:
                                    order = self.sell(size=position_size)
                                    
                                if order:
                                    self.order_count += 1
                                else:
                                    self.order_rejections += 1
                            except Exception as e:
                                self.order_rejections += 1
                                if self.order_rejections <= 3:
                                    print(f"      ❌ Order exception: {e}")
                        else:
                            self.validation_failures += 1
                            
                except Exception as e:
                    if not hasattr(self, 'rule_errors'):
                        self.rule_errors = 0
                    self.rule_errors += 1
                    if self.rule_errors <= 3:
                        print(f"      ❌ Rule evaluation error: {e}")
            
            def _validate_signal(self, signal):
                """Validate exactly like Cortex does"""
                entry = signal.get('entry_price')
                sl = signal.get('stop_loss')
                tp = signal.get('take_profit')
                
                if not all([entry, sl, tp]):
                    return False
                    
                if signal['direction'] == 'long':
                    if sl >= entry or tp <= entry:
                        return False
                    if (entry - sl) < 0.5 or (tp - entry) < 0.5:
                        return False
                
                return True
        
        # Run backtest exactly like Cortex does
        try:
            bt = Backtest(
                ohlc_data,
                JaegerPatternStrategy,
                cash=config.DEFAULT_INITIAL_CASH,
                spread=config.DEFAULT_SPREAD,
                commission=config.DEFAULT_COMMISSION,
                margin=config.DEFAULT_MARGIN,
                exclusive_orders=True
            )
            
            stats = bt.run()
            strategy = stats._strategy
            
            print(f"   📊 Results for Pattern {i}:")
            print(f"      Signals: {strategy.signal_count}")
            print(f"      Validation failures: {strategy.validation_failures}")
            print(f"      Orders: {strategy.order_count}")
            print(f"      Order rejections: {strategy.order_rejections}")
            print(f"      Trades: {stats['# Trades']}")
            
            if strategy.signal_count == 0:
                print(f"      ❌ NO SIGNALS - Pattern condition never met in data")
            elif stats['# Trades'] > 0:
                print(f"      ✅ SUCCESS - {stats['# Trades']} trades executed")
            else:
                print(f"      ❌ SIGNALS BUT NO TRADES")
                
        except Exception as e:
            print(f"   ❌ Backtest failed: {e}")

if __name__ == "__main__":
    debug_exact_jaeger_flow()
