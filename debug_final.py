#!/usr/bin/env python3
"""
Final debug to identify the exact issue
"""

import sys
sys.path.append('src')

import pandas as pd
from data_ingestion import DataIngestionManager
from llm_rule_parser import LLMRuleParser
from backtesting import Backtest, Strategy
from config import <PERSON><PERSON><PERSON><PERSON>onfig

def debug_final():
    """Final debug to find the exact issue"""
    print("🔍 FINAL DEBUG - FINDING THE EXACT ISSUE")
    print("=" * 50)
    
    config = JaegerConfig()
    
    # Load data
    data_file = "data/2025.6.23GBRIDXGBP_M1_UTCPlus01-M1-No Session.csv"
    ingestion_manager = DataIngestionManager()
    raw_data = ingestion_manager.load_market_data(data_file)
    ohlc_data = ingestion_manager.prepare_for_backtesting(raw_data)
    
    print(f"✅ Data: {len(ohlc_data)} records")
    
    # Parse pattern
    pattern_text = """
### Pattern 1: Bullish Breakout
**MT4 Entry:** Close[0] > High[1]
**MT4 Direction:** OP_BUY
**MT4 Stop:** Low[1]
**MT4 Target:** Close[0] + (Close[0] - Low[1]) * 2.0
**MT4 Position Size:** 1 unit
"""
    
    parser = LLMRuleParser()
    rules = parser.parse_llm_response(pattern_text)
    rule_func = parser._create_python_function(rules[0])
    
    print(f"✅ Pattern parsed")
    
    # Test rule function on small sample
    test_data = ohlc_data.head(100)
    signals_found = 0
    
    print(f"\n🔍 Testing rule function directly...")
    for i in range(2, min(10, len(test_data))):
        signal = rule_func(test_data, i)
        if signal:
            signals_found += 1
            print(f"🎯 Signal {signals_found}: entry={signal['entry_price']:.1f}, sl={signal['stop_loss']:.1f}, tp={signal['take_profit']:.1f}")
    
    print(f"✅ Found {signals_found} signals in first 10 bars")
    
    # Now test with backtesting - EXACT same as Cortex
    print(f"\n🔍 Testing with backtesting library...")
    
    class TestStrategy(Strategy):
        def init(self):
            self.signal_counter = 0
            self.order_counter = 0
            
        def next(self):
            if len(self.data.Close) < 3:
                return
                
            current_idx = len(self.data.Close) - 1
            
            # Use the EXACT same data that Cortex uses
            signal = rule_func(test_data, current_idx)
            
            if signal:
                self.signal_counter += 1
                print(f"📊 Backtest signal {self.signal_counter} at bar {current_idx}")
                print(f"   Entry: {signal['entry_price']:.1f}")
                print(f"   Stop: {signal['stop_loss']:.1f}")
                print(f"   Target: {signal['take_profit']:.1f}")
                print(f"   Size: {signal['position_size']}")
                
                # Test order placement
                try:
                    position_size = signal.get('position_size', 1.0)
                    print(f"   🔍 Attempting to place order with size {position_size}")
                    
                    order = self.buy(size=position_size)
                    if order:
                        self.order_counter += 1
                        print(f"   ✅ Order {self.order_counter} placed successfully!")
                    else:
                        print(f"   ❌ Order returned None")
                        
                except Exception as e:
                    print(f"   ❌ Order placement failed: {e}")
                    import traceback
                    traceback.print_exc()
    
    try:
        print(f"🔄 Running backtest with exact Cortex settings...")
        bt = Backtest(
            test_data,
            TestStrategy,
            cash=config.DEFAULT_INITIAL_CASH,
            spread=config.DEFAULT_SPREAD,
            commission=config.DEFAULT_COMMISSION,
            margin=config.DEFAULT_MARGIN,
            exclusive_orders=True
        )
        
        stats = bt.run()
        strategy = stats._strategy
        
        print(f"\n📊 FINAL RESULTS:")
        print(f"   Signals generated: {strategy.signal_counter}")
        print(f"   Orders placed: {strategy.order_counter}")
        print(f"   Trades executed: {stats['# Trades']}")
        print(f"   Return: {stats['Return [%]']:.2f}%")
        
        # Analyze the issue
        if strategy.signal_counter == 0:
            print("\n❌ ISSUE: No signals generated in backtest")
            print("   This means the rule function is not working in backtest context")
        elif strategy.order_counter == 0:
            print("\n❌ ISSUE: Signals generated but no orders placed")
            print("   This means order placement is failing")
        elif stats['# Trades'] == 0:
            print("\n❌ ISSUE: Orders placed but no trades executed")
            print("   This means orders are being rejected by the backtesting engine")
        else:
            print("\n✅ SUCCESS: Everything working correctly!")
            
        # Show configuration for debugging
        print(f"\n🔧 Configuration used:")
        print(f"   Cash: ${config.DEFAULT_INITIAL_CASH:,.0f}")
        print(f"   Margin: {config.DEFAULT_MARGIN}")
        print(f"   Spread: {config.DEFAULT_SPREAD}")
        print(f"   Commission: {config.DEFAULT_COMMISSION}")
        
    except Exception as e:
        print(f"❌ Backtest failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_final()
