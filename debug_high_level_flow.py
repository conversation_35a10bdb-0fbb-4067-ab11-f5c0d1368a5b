#!/usr/bin/env python3
"""
High-level debug to understand the complete flow from signals to trades
"""

import sys
sys.path.append('src')

import pandas as pd
import numpy as np
from backtesting import Backtest, Strategy
from config import <PERSON><PERSON><PERSON>Config

def debug_high_level_flow():
    """Debug the complete high-level flow"""
    print("🔍 HIGH-LEVEL FLOW DEBUG")
    print("=" * 50)
    
    config = JaegerConfig()
    
    # Create simple test data
    dates = pd.date_range('2024-01-01', periods=100, freq='1min')
    test_data = pd.DataFrame({
        'Open': np.random.uniform(8300, 8350, 100),
        'High': np.random.uniform(8320, 8370, 100),
        'Low': np.random.uniform(8280, 8330, 100),
        'Close': np.random.uniform(8300, 8350, 100),
        'Volume': np.random.randint(1000, 5000, 100)
    }, index=dates)
    
    # Ensure some breakout conditions (Close[0] > High[1])
    for i in range(10, 20):
        test_data.iloc[i]['Close'] = test_data.iloc[i-1]['High'] + 5
    
    print(f"📊 Test data: {len(test_data)} bars")
    print(f"📊 Price range: {test_data['Close'].min():.1f} - {test_data['Close'].max():.1f}")
    
    # Create a simple strategy that mimics what Cortex does
    class DebugStrategy(Strategy):
        def init(self):
            self.signal_count = 0
            self.order_count = 0
            self.trade_count = 0
            self.validation_failures = 0
            self.order_rejections = 0
            
        def next(self):
            current_idx = len(self.data.Close) - 1
            if current_idx < 2:
                return
                
            # Simple breakout condition: Close[0] > High[1]
            current_close = self.data.Close[-1]
            previous_high = self.data.High[-2]
            
            if current_close > previous_high:
                self.signal_count += 1
                
                # Create signal like LLM rule parser would
                signal = {
                    'direction': 'long',
                    'entry_price': current_close,
                    'stop_loss': self.data.Low[-2],  # Low[1]
                    'take_profit': current_close + (current_close - self.data.Low[-2]) * 2.0,
                    'position_size': 1.0,  # Absolute units
                    'rule_id': 1
                }
                
                print(f"🎯 Signal {self.signal_count} at bar {current_idx}:")
                print(f"   Entry: {signal['entry_price']:.1f}")
                print(f"   Stop: {signal['stop_loss']:.1f}")
                print(f"   Target: {signal['take_profit']:.1f}")
                print(f"   Size: {signal['position_size']}")
                
                # Validate like Cortex does
                if self._validate_signal(signal):
                    try:
                        print(f"   📊 Placing order...")
                        order = self.buy(size=signal['position_size'])
                        if order:
                            self.order_count += 1
                            print(f"   ✅ Order placed successfully!")
                            
                            # Check if it becomes a trade
                            if len(self.trades) > self.trade_count:
                                self.trade_count = len(self.trades)
                                print(f"   🎉 TRADE EXECUTED! Total trades: {self.trade_count}")
                        else:
                            self.order_rejections += 1
                            print(f"   ❌ Order returned None")
                    except Exception as e:
                        self.order_rejections += 1
                        print(f"   ❌ Order exception: {e}")
                else:
                    self.validation_failures += 1
                    print(f"   ❌ Signal validation failed")
        
        def _validate_signal(self, signal):
            """Validate signal like Cortex does"""
            entry = signal['entry_price']
            sl = signal['stop_loss']
            tp = signal['take_profit']
            
            if not all([entry, sl, tp]):
                print(f"      Missing parameters: entry={entry}, sl={sl}, tp={tp}")
                return False
                
            if signal['direction'] == 'long':
                if sl >= entry:
                    print(f"      LONG: SL ({sl:.1f}) >= Entry ({entry:.1f})")
                    return False
                if tp <= entry:
                    print(f"      LONG: TP ({tp:.1f}) <= Entry ({entry:.1f})")
                    return False
                    
                min_distance = 0.5
                if (entry - sl) < min_distance:
                    print(f"      LONG: SL too close (distance: {entry - sl:.1f})")
                    return False
                if (tp - entry) < min_distance:
                    print(f"      LONG: TP too close (distance: {tp - entry:.1f})")
                    return False
            
            return True
    
    # Run backtest
    print(f"\n🔄 Running backtest...")
    print(f"   Cash: ${config.DEFAULT_INITIAL_CASH:,.0f}")
    print(f"   Margin: {config.DEFAULT_MARGIN}")
    print(f"   Spread: {config.DEFAULT_SPREAD}")
    
    try:
        bt = Backtest(
            test_data,
            DebugStrategy,
            cash=config.DEFAULT_INITIAL_CASH,
            spread=config.DEFAULT_SPREAD,
            commission=config.DEFAULT_COMMISSION,
            margin=config.DEFAULT_MARGIN,
            exclusive_orders=True
        )
        
        stats = bt.run()
        strategy = stats._strategy
        
        print(f"\n🎯 HIGH-LEVEL RESULTS:")
        print(f"   📊 Signals generated: {strategy.signal_count}")
        print(f"   📊 Validation failures: {strategy.validation_failures}")
        print(f"   📊 Orders attempted: {strategy.order_count}")
        print(f"   📊 Order rejections: {strategy.order_rejections}")
        print(f"   📊 Trades executed: {stats['# Trades']}")
        print(f"   📈 Return: {stats['Return [%]']:.2f}%")
        
        # Analyze the flow
        print(f"\n🔍 FLOW ANALYSIS:")
        if strategy.signal_count == 0:
            print("   ❌ NO SIGNALS GENERATED - Pattern condition not met")
        elif strategy.validation_failures > 0:
            print(f"   ❌ VALIDATION FAILURES: {strategy.validation_failures}/{strategy.signal_count}")
        elif strategy.order_rejections > 0:
            print(f"   ❌ ORDER REJECTIONS: {strategy.order_rejections}/{strategy.signal_count}")
        elif stats['# Trades'] == 0:
            print("   ❌ ORDERS PLACED BUT NO TRADES EXECUTED")
        else:
            print("   ✅ COMPLETE SUCCESS - Signals → Orders → Trades")
            
        # Show margin info
        print(f"\n💰 MARGIN ANALYSIS:")
        print(f"   Available cash: ${config.DEFAULT_INITIAL_CASH:,.0f}")
        print(f"   Leverage: {1/config.DEFAULT_MARGIN:.0f}:1")
        print(f"   Price level: ~{test_data['Close'].mean():.0f}")
        print(f"   1 unit margin required: ${test_data['Close'].mean() * config.DEFAULT_MARGIN:,.2f}")
        
    except Exception as e:
        print(f"❌ Backtest failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_high_level_flow()
