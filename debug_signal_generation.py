#!/usr/bin/env python3
"""
Debug why no signals are being generated in the actual <PERSON><PERSON><PERSON> run
"""

import sys
sys.path.append('src')

import pandas as pd
import numpy as np
from data_ingestion import DataIngestionManager
from llm_rule_parser import LLMRuleParser
from config import <PERSON><PERSON><PERSON>Config

def debug_signal_generation():
    """Debug why patterns generate 0 signals in real data"""
    print("🔍 DEBUGGING SIGNAL GENERATION IN REAL DATA")
    print("=" * 60)
    
    config = JaegerConfig()
    
    # Use the exact same data file that <PERSON><PERSON><PERSON> uses
    data_file = "data/2025.6.23GBRIDXGBP_M1_UTCPlus01-M1-No Session.csv"
    
    print(f"📊 Loading data from: {data_file}")
    
    # Load data exactly like Cortex does
    try:
        ingestion_manager = DataIngestionManager()
        raw_data = ingestion_manager.load_market_data(data_file)
        ohlc_data = ingestion_manager.prepare_for_backtesting(raw_data)
        print(f"✅ Data loaded: {len(ohlc_data)} records")
        print(f"   Date range: {ohlc_data.index.min()} to {ohlc_data.index.max()}")
        print(f"   Price range: {ohlc_data['Close'].min():.1f} - {ohlc_data['Close'].max():.1f}")
    except Exception as e:
        print(f"❌ Data loading failed: {e}")
        return
    
    # Use the exact patterns from the latest Jaeger run
    pattern_text = """
### Pattern 1: Bullish Breakout

**Market Logic:** This pattern works by identifying a bullish breakout where the current close is higher than the previous high, indicating a potential upward trend.

**MT4 Entry:** Close[0] > High[1]
**MT4 Direction:** OP_BUY
**MT4 Stop:** Low[1]
**MT4 Target:** Close[0] + (Close[0] - Low[1]) * 2.0
**MT4 Position Size:** 1 unit
**MT4 Timeframe:** PERIOD_M5

### Pattern 2: Bearish Reversal

**Market Logic:** This pattern works by identifying a bearish reversal where the current close is lower than the previous low, indicating a potential downward trend.

**MT4 Entry:** Close[0] < Low[1]
**MT4 Direction:** OP_SELL
**MT4 Stop:** High[1]
**MT4 Target:** Close[0] - (High[1] - Close[0]) * 1.5
**MT4 Position Size:** 1 unit
**MT4 Timeframe:** PERIOD_M5
"""
    
    print(f"📋 Parsing patterns exactly like Cortex does...")
    
    # Parse patterns exactly like Cortex does
    try:
        parser = LLMRuleParser()
        rules = parser.parse_llm_response(pattern_text)
        print(f"✅ Parsed {len(rules)} rules")
        
        # Create rule functions exactly like Cortex does
        rule_functions = []
        for rule in rules:
            rule_func = parser._create_python_function(rule)
            if rule_func:
                rule_functions.append(rule_func)
        
        print(f"✅ Created {len(rule_functions)} rule functions")
        
    except Exception as e:
        print(f"❌ Pattern parsing failed: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Test each pattern on a sample of data to see why no signals
    print(f"\n🔍 TESTING PATTERN EVALUATION ON REAL DATA...")
    
    # Test on first 1000 bars to see what's happening
    test_data = ohlc_data.head(1000).copy()
    
    for i, rule_func in enumerate(rule_functions, 1):
        print(f"\n🔍 Testing Pattern {i} on first 1000 bars...")
        
        signal_count = 0
        error_count = 0
        
        # Test each bar
        for idx in range(2, len(test_data)):  # Start from bar 2 (need previous bars)
            try:
                signal = rule_func(test_data, idx)
                if signal:
                    signal_count += 1
                    if signal_count <= 3:  # Show first few signals
                        print(f"      🎯 Signal {signal_count} at bar {idx}:")
                        print(f"         Entry: {signal.get('entry_price', 'N/A')}")
                        print(f"         Stop: {signal.get('stop_loss', 'N/A')}")
                        print(f"         Target: {signal.get('take_profit', 'N/A')}")
                        print(f"         Direction: {signal.get('direction', 'N/A')}")
                        
                        # Show the actual data values
                        current_close = test_data.iloc[idx]['Close']
                        prev_high = test_data.iloc[idx-1]['High']
                        prev_low = test_data.iloc[idx-1]['Low']
                        
                        print(f"         Data: Close[0]={current_close:.1f}, High[1]={prev_high:.1f}, Low[1]={prev_low:.1f}")
                        
                        if i == 1:  # Pattern 1: Close[0] > High[1]
                            print(f"         Condition: {current_close:.1f} > {prev_high:.1f} = {current_close > prev_high}")
                        elif i == 2:  # Pattern 2: Close[0] < Low[1]
                            print(f"         Condition: {current_close:.1f} < {prev_low:.1f} = {current_close < prev_low}")
                            
            except Exception as e:
                error_count += 1
                if error_count <= 3:
                    print(f"      ❌ Error at bar {idx}: {e}")
        
        print(f"   📊 Pattern {i} Results on 1000 bars:")
        print(f"      Total signals: {signal_count}")
        print(f"      Total errors: {error_count}")
        
        if signal_count == 0:
            print(f"      ❌ NO SIGNALS GENERATED - Pattern condition never met")
            
            # Let's check why by examining the data
            print(f"      🔍 Analyzing why no signals...")
            
            if i == 1:  # Pattern 1: Close[0] > High[1]
                breakouts = 0
                for idx in range(2, min(100, len(test_data))):
                    current_close = test_data.iloc[idx]['Close']
                    prev_high = test_data.iloc[idx-1]['High']
                    if current_close > prev_high:
                        breakouts += 1
                        if breakouts <= 3:
                            print(f"         Found breakout at bar {idx}: {current_close:.1f} > {prev_high:.1f}")
                print(f"      📊 Found {breakouts} breakouts in first 100 bars")
                
            elif i == 2:  # Pattern 2: Close[0] < Low[1]
                breakdowns = 0
                for idx in range(2, min(100, len(test_data))):
                    current_close = test_data.iloc[idx]['Close']
                    prev_low = test_data.iloc[idx-1]['Low']
                    if current_close < prev_low:
                        breakdowns += 1
                        if breakdowns <= 3:
                            print(f"         Found breakdown at bar {idx}: {current_close:.1f} < {prev_low:.1f}")
                print(f"      📊 Found {breakdowns} breakdowns in first 100 bars")
        
        else:
            print(f"      ✅ Pattern {i} generates signals correctly")
    
    # Also check if there's an issue with the rule function creation
    print(f"\n🔍 CHECKING RULE FUNCTION CREATION...")
    for i, rule in enumerate(rules, 1):
        print(f"   Rule {i}:")
        print(f"      Entry condition: {rule.entry_condition}")
        print(f"      Direction: {rule.direction}")
        print(f"      Stop loss: {rule.stop_loss}")
        print(f"      Take profit: {rule.take_profit}")

if __name__ == "__main__":
    debug_signal_generation()
