#!/usr/bin/env python3
"""
Simple debug to find the exact issue
"""

import sys
sys.path.append('src')

import pandas as pd
from data_ingestion import DataIngestionManager
from llm_rule_parser import LLMRuleParser
from backtesting import Backtest, Strategy
from config import <PERSON><PERSON><PERSON><PERSON>onfig

def debug_simple():
    """Simple debug to find the issue"""
    print("🔍 SIMPLE CORTEX DEBUG")
    print("=" * 30)
    
    config = JaegerConfig()
    
    # Load data
    data_file = "data/2025.6.23GBRIDXGBP_M1_UTCPlus01-M1-No Session.csv"
    ingestion_manager = DataIngestionManager()
    raw_data = ingestion_manager.load_market_data(data_file)
    ohlc_data = ingestion_manager.prepare_for_backtesting(raw_data)
    
    print(f"✅ Data: {len(ohlc_data)} records")
    
    # Parse pattern
    pattern_text = """
### Pattern 1: Bullish Breakout
**MT4 Entry:** Close[0] > High[1]
**MT4 Direction:** OP_BUY
**MT4 Stop:** Low[1]
**MT4 Target:** Close[0] + (Close[0] - Low[1]) * 2.0
**MT4 Position Size:** 1 unit
"""
    
    parser = LLMRuleParser()
    rules = parser.parse_llm_response(pattern_text)
    rule_func = parser._create_python_function(rules[0])
    
    print(f"✅ Pattern parsed")
    
    # Test rule function on small sample
    test_data = ohlc_data.head(100)
    signals_found = 0
    
    for i in range(2, len(test_data)):
        signal = rule_func(test_data, i)
        if signal:
            signals_found += 1
            if signals_found <= 3:
                print(f"🎯 Signal {signals_found}: entry={signal['entry_price']:.1f}, sl={signal['stop_loss']:.1f}, tp={signal['take_profit']:.1f}")
    
    print(f"✅ Found {signals_found} signals in 100 bars")
    
    # Now test with backtesting
    class TestStrategy(Strategy):
        def init(self):
            self.signals = 0
            self.orders = 0
            self.trades = 0
            
        def next(self):
            if len(self.data.Close) < 3:
                return
                
            current_idx = len(self.data.Close) - 1
            signal = rule_func(test_data, current_idx)
            
            if signal:
                self.signals += 1
                print(f"📊 Backtest signal {self.signals} at bar {current_idx}")
                
                try:
                    order = self.buy(size=1.0)
                    if order:
                        self.orders += 1
                        print(f"✅ Order {self.orders} placed")
                    else:
                        print(f"❌ Order returned None")
                except Exception as e:
                    print(f"❌ Order failed: {e}")
    
    print(f"\n🔄 Running backtest...")
    bt = Backtest(test_data, TestStrategy, cash=100000, margin=0.02)
    stats = bt.run()
    
    strategy = stats._strategy
    print(f"📊 Backtest results:")
    print(f"   Signals: {strategy.signals}")
    print(f"   Orders: {strategy.orders}")
    print(f"   Trades: {stats['# Trades']}")
    print(f"   Return: {stats['Return [%]']:.2f}%")
    
    if strategy.signals > 0 and stats['# Trades'] == 0:
        print("❌ SIGNALS GENERATED BUT NO TRADES - This is the issue!")
    elif strategy.signals == 0:
        print("❌ NO SIGNALS GENERATED - Pattern not working in backtest")
    else:
        print("✅ Everything working!")

if __name__ == "__main__":
    debug_simple()
