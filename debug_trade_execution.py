#!/usr/bin/env python3
"""
Debug trade execution issue - minimal test
"""

import sys
sys.path.append('src')

from backtesting import Backtest, Strategy
import pandas as pd
import numpy as np
from config import <PERSON><PERSON><PERSON>Config

def test_minimal_trade_execution():
    """Test minimal trade execution with backtesting library"""
    print("🔍 DEBUGGING TRADE EXECUTION")
    print("=" * 50)
    
    config = JaegerConfig()
    
    # Create minimal test data
    dates = pd.date_range('2024-01-01', periods=100, freq='1min')
    test_data = pd.DataFrame({
        'Open': np.random.uniform(8300, 8350, 100),
        'High': np.random.uniform(8320, 8370, 100),
        'Low': np.random.uniform(8280, 8330, 100),
        'Close': np.random.uniform(8300, 8350, 100),
        'Volume': np.random.randint(1000, 5000, 100)
    }, index=dates)
    
    print(f"📊 Test data: {len(test_data)} bars")
    print(f"📊 Price range: {test_data['Close'].min():.1f} - {test_data['Close'].max():.1f}")
    
    class MinimalStrategy(Strategy):
        def init(self):
            self.order_count = 0
            self.signal_count = 0
            
        def next(self):
            # Simple condition: buy on every 10th bar
            if len(self.data) % 10 == 0 and len(self.data) > 10:
                self.signal_count += 1
                
                # Test different position sizes
                test_sizes = [1.0, 2.0, 0.1, 0.01]
                size = test_sizes[self.signal_count % len(test_sizes)]
                
                print(f"📊 Bar {len(self.data)}: Testing position size {size}")
                print(f"   Current price: {self.data.Close[-1]:.1f}")
                print(f"   Available cash: ${self._broker._cash:.2f}")
                print(f"   Current equity: ${self._broker.equity:.2f}")
                
                try:
                    # Test market order
                    order = self.buy(size=size)
                    if order:
                        self.order_count += 1
                        print(f"   ✅ Order placed successfully: {order}")
                        print(f"   Order size: {order.size}")
                        # Order price not available until filled
                    else:
                        print(f"   ❌ Order returned None")
                        
                        # Debug why order failed
                        required_cash = size * self.data.Close[-1]
                        print(f"   Required cash: ${required_cash:.2f}")
                        print(f"   Available cash: ${self._broker._cash:.2f}")
                        print(f"   Margin: {config.DEFAULT_MARGIN}")
                        
                        if config.DEFAULT_MARGIN > 0:
                            required_margin = required_cash * config.DEFAULT_MARGIN
                            print(f"   Required margin: ${required_margin:.2f}")
                        
                except Exception as e:
                    print(f"   ❌ Order exception: {e}")
                    import traceback
                    traceback.print_exc()
    
    # Run backtest with different configurations
    print(f"\n🔄 Running backtest...")
    print(f"   Initial cash: ${config.DEFAULT_INITIAL_CASH:,.0f}")
    print(f"   Margin: {config.DEFAULT_MARGIN}")
    print(f"   Spread: {config.DEFAULT_SPREAD}")
    print(f"   Commission: {config.DEFAULT_COMMISSION}")
    
    try:
        bt = Backtest(
            test_data,
            MinimalStrategy,
            cash=config.DEFAULT_INITIAL_CASH,
            spread=config.DEFAULT_SPREAD,
            commission=config.DEFAULT_COMMISSION,
            margin=config.DEFAULT_MARGIN,
            exclusive_orders=True
        )
        
        stats = bt.run()
        
        print(f"\n🎯 BACKTEST RESULTS:")
        print(f"   📊 Signals generated: {getattr(stats._strategy, 'signal_count', 'N/A')}")
        print(f"   📊 Orders attempted: {getattr(stats._strategy, 'order_count', 'N/A')}")
        print(f"   📊 Trades executed: {stats['# Trades']}")
        print(f"   📈 Return: {stats['Return [%]']:.2f}%")
        print(f"   💰 Final equity: ${stats['Equity Final [$]']:,.2f}")
        
        if stats['# Trades'] > 0:
            print(f"✅ SUCCESS! Trades were executed!")
        else:
            print(f"❌ ZERO TRADES EXECUTED - CRITICAL ISSUE!")
            
        # Show all stats for debugging
        print(f"\n📊 All stats:")
        for key, value in stats.items():
            if not key.startswith('_'):
                print(f"   {key}: {value}")
                
    except Exception as e:
        print(f"❌ Backtest failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_minimal_trade_execution()
