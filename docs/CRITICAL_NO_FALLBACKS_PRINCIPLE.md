# 🚨 CRITICAL: NO FALLBACKS PRINCIPLE

## ⚠️ UNBREAKABLE RULE: ZERO FALLBACKS ANYWHERE

**The Jaeger trading system must have ZERO fallbacks anywhere in the codebase.**

### 🎯 Core Principle

**The system must either work exactly as the LLM intended or fail completely.**

**Fallbacks are EXPRESSLY FORBIDDEN and cause catastrophic damage to the project.**

### 🔥 Why Fallbacks Are Catastrophic

1. **Silent Corruption**: Fallbacks mask LLM rule failures, making debugging impossible
2. **False Confidence**: System appears to work but executes wrong logic
3. **Data Integrity**: Fallbacks corrupt the learning feedback loop
4. **Trust Erosion**: Users lose confidence when system behaves unexpectedly
5. **Hidden Bugs**: Real issues get buried under fallback behavior

### ❌ FORBIDDEN Fallback Examples

```python
# FORBIDDEN - Default stop loss fallback
if direction == 'long':
    return entry_price * 0.992  # 0.8% below entry
else:
    return entry_price * 1.008  # 0.8% above entry

# FORBIDDEN - De<PERSON>ult take profit fallback  
if direction == 'long':
    return entry_price * 1.012  # 1.2% above entry
else:
    return entry_price * 0.988  # 1.2% below entry

# FORBIDDEN - Default percentage thresholds
default_threshold = 0.005  # 0.5% default
threshold_pct = float(pct_match.group(1)) / 100 if pct_match else default_threshold

# FORBIDDEN - Arbitrary rule generation
rule_hash = hash(rule.entry_condition) % 1000
base_pct = 0.002 + (rule_hash % 20) * 0.0001
```

### ✅ CORRECT Approach

```python
# CORRECT - Fail cleanly when LLM doesn't provide complete rules
if not valid_stop_loss:
    raise RuleParseError(f"LLM must provide a valid stop loss rule. Got: '{stop_loss_rule}'")

if not valid_take_profit:
    raise RuleParseError(f"LLM must provide a valid take profit rule. Got: '{exit_condition}'")

if not recognizable_entry_condition:
    raise RuleParseError(f"LLM must provide a recognizable entry condition. Got: '{entry_condition}'")
```

### 🎯 Implementation Requirements

1. **LLM Rule Parser**: Must reject incomplete rules with clear error messages
2. **Trading Strategy**: Must fail if any rule component is missing
3. **MT4 Generation**: Must fail if any rule cannot be translated
4. **Backtesting**: Must fail if any signal is invalid
5. **Configuration**: Must fail if required parameters are missing

### 🔍 Detection Methods

**Code Review Checklist:**
- [ ] No `default_` variables anywhere
- [ ] No `fallback_` logic anywhere  
- [ ] No `if ... else: return default_value` patterns
- [ ] All error conditions raise exceptions
- [ ] No silent failures or assumptions

**Search Patterns to Eliminate:**
```bash
grep -r "default_" src/
grep -r "fallback" src/
grep -r "else.*return.*\*" src/
grep -r "0\.00[0-9]" src/  # Hard-coded percentages
```

### 🚀 Benefits of No-Fallback Design

1. **Immediate Feedback**: LLM learns from failures quickly
2. **Clean Debugging**: Issues are obvious and traceable
3. **Data Integrity**: All results reflect actual LLM decisions
4. **Trust Building**: System behavior is predictable and transparent
5. **Rapid Iteration**: Problems surface immediately for fixing

### 📋 Enforcement

**This principle must be enforced at:**
- Code review level
- Testing level  
- Documentation level
- Architecture level

**Any fallback code is considered a critical bug and must be removed immediately.**

### 🎯 Remember

**"Better to fail fast and learn than to succeed with wrong logic."**

The Jaeger system's intelligence comes from the LLM. Any fallback logic undermines this intelligence and corrupts the entire system.

**NO EXCEPTIONS. NO COMPROMISES. NO FALLBACKS.**
