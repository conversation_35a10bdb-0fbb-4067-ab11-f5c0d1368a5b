![Jaeger Logo](../branding/jaeger-logo.png)

# 🧪 Jaeger Testing Standards

## 🚨 UNBREAKABLE RULES

### 🔥 FAIL HARD PRINCIPLE - 100% OR NOTHING
**See [FAIL_HARD_PRINCIPLE.md](FAIL_HARD_PRINCIPLE.md) for complete documentation**

#### **🎯 FINANCIAL SOFTWARE ABSOLUTE REQUIREMENT:**
**<PERSON><PERSON><PERSON> must work at 100% as intended or not work at all.**

- ❌ **NO GRACEFUL DEGRADATION** - System works exactly or fails completely
- ❌ **NO FALLBACKS** - No alternative paths when things fail
- ❌ **NO ERROR RECOVERY** - Let the system crash on errors
- ❌ **NO PARTIAL FUNCTIONALITY** - No "good enough" or "mostly working"
- ❌ **NO APPROXIMATIONS** - No "close enough" results
- ✅ **FAIL FAST, FAIL LOUD** - Raise exceptions immediately
- ✅ **BINARY OPERATION** - Works perfectly or doesn't work at all
- ✅ **100% RELIABILITY** - Nothing less than perfect functionality is acceptable

#### **🏦 FINANCIAL PROJECT RATIONALE:**
- **Financial accuracy is non-negotiable** - Wrong results can cause financial losses
- **Better to not trade than trade incorrectly** - No trading is safer than bad trading
- **Intermittent operation model** - Jaeger runs occasionally, analyzes, then shuts down
- **No 24/7 uptime requirement** - System doesn't need to "stay running" with degraded performance
- **Risk management priority** - Financial software must be 100% reliable or completely offline

### 🚨 REAL DATA ONLY

**NEVER USE SYNTHETIC DATA** - This is an absolute, non-negotiable requirement for all testing in the Jaeger system.

### ❌ ABSOLUTELY FORBIDDEN - HARD RULE:
- **`np.random` data generation** - Any use of numpy.random for market data
- **Artificial OHLC creation** - No `for i in range()` loops creating fake prices
- **Mock market data** - No mocked price feeds or stubbed data
- **Synthetic trade results** - No fabricated R-multiples or win rates
- **Fallback synthetic data** - No synthetic data when real data is missing
- **Mathematical price models** - No algorithmic price generation
- **Fabricated market scenarios** - No made-up trading conditions

### 🔒 HARD RULE ENFORCEMENT:
- **ZERO EXCEPTIONS** - No synthetic data under any circumstances
- **FAIL HARD** - Tests must raise `FileNotFoundError` with "UNBREAKABLE RULE VIOLATION" if real data missing
- **NO FALLBACKS** - No graceful degradation to synthetic data
- **CODE REVIEW BLOCKER** - Any synthetic data usage blocks merge

### ✅ REQUIRED PRACTICES:
- **Real market data only** - Authentic DEUIDXEUR market data from `/data` directory
- **Direct data usage** - Tests use real data files directly from `/data/`
- **Authentic conditions** - All tests must use real market conditions
- **Production validation** - Test with data that represents actual trading scenarios
- **NO SYNTHETIC DATA EVER** - Absolute prohibition on any form of artificial data

## 📊 Real Data Infrastructure

### **Data Source:**
- **Market**: DEUIDXEUR (German index EUR) - Real institutional-grade data
- **Records**: 332,436 authentic 1-minute bars
- **Coverage**: Full year of real market activity (2024-05-27 to 2025-05-26)
- **Format**: OHLC + Volume + DateTime
- **Quality**: Production-grade data used by actual traders
- **Location**: `/data/2025.6.17DEUIDXEUR_M1_UTCPlus01-M1-No Session.csv`

### **Test Data Usage:**
```
/data/
└── 2025.6.17DEUIDXEUR_M1_UTCPlus01-M1-No Session.csv  # 332,436 real market records
```

### **Direct Data Access:**
```python
# Tests use real data directly from /data directory
data_file = os.path.join('data', '2025.6.17DEUIDXEUR_M1_UTCPlus01-M1-No Session.csv')
data = pd.read_csv(data_file)  # 332,436 real market records
```

## ✅ Test Suite Status

### **Core Module Test Success:**
- **Configuration Tests**: 10/10 passing ✅
- **LM Studio Client Tests**: 21/21 passing ✅
- **Fact Checker Tests**: 22/22 passing ✅
- **Total Core Tests**: 53/53 passing (100% success rate) ✅
- **Real Data Compliance**: All tests use authentic market data

### **Test Categories:**

#### **1. Backtester Tests (`test_backtester.py`)**
- **Data Source**: `dax_200_bars.csv` (real DAX data)
- **Price Levels**: DAX-appropriate (18700+) not forex (1.1000)
- **Trading Rules**: Realistic breakout patterns for index trading
- **Validation**: Authentic trade generation and R-multiple calculations

#### **2. Cortex Tests (`test_cortex.py`)**
- **Data Source**: Multiple real data files based on test requirements
- **Processing**: Real data loading, cleaning, and preparation
- **Analysis**: Authentic market situation recognition
- **Validation**: Real profitability criteria with actual market conditions

#### **3. Integration Tests (`test_integration_end_to_end.py`)**
- **Data Source**: `dax_500_bars.csv` (real DAX data)
- **Workflow**: Complete end-to-end testing with real data
- **Validation**: Authentic system behavior and pattern rejection
- **Results**: Proper handling of unprofitable patterns

## 🎯 Testing Principles

### **1. Authenticity First**
- Every test must use real market data
- No exceptions for "convenience" or "speed"
- Real data ensures production-ready validation

### **2. Realistic Scenarios**
- Test with actual market volatility
- Use real price movements and gaps
- Validate with authentic trading conditions

### **3. Production Validation**
- System must handle real market irregularities
- Patterns must work with actual data characteristics
- **TRUE dynamic profitability** accepts any profitable combination

### **4. TRUE Dynamic Validation Testing**
- Test simple profitability calculation (total PnL > 0)
- Verify any profitable combination is accepted
- Ensure no hardcoded thresholds are applied
- Test universal compatibility across instruments and timeframes
- Validate maximum success rate in finding profitable patterns

### **5. 🎯 Dynamic Risk Management Testing**
- **LLM Risk Analysis Validation** - Test AI-driven risk percentage determination
- **Pattern-Specific Risk Testing** - Verify each pattern gets unique risk %
- **Three-Phase Workflow Testing** - Validate Discovery → Analysis → Validation phases
- **Boundary Compliance Testing** - Ensure risk stays within user-defined limits
- **Portfolio Risk Validation** - Test total portfolio risk calculations
- **Real Pattern Risk Analysis** - Use authentic patterns for risk determination testing

### **4. Quality Assurance**
- Real data reveals actual system behavior
- Authentic validation prevents false positives
- Production-ready testing ensures reliability

## 🔧 Implementation Guidelines

### **For New Tests:**
1. **Always start with real data** - Never create synthetic data
2. **Extract appropriate size** - Use existing RealTestData files
3. **Use realistic parameters** - DAX price levels, not forex
4. **Validate authentically** - Test real market behavior

### **For Existing Tests:**
1. **Replace synthetic data** - Convert all mocks to real data
2. **Update price levels** - Change forex to DAX levels
3. **Fix expectations** - Adjust for real market behavior
4. **Validate results** - Ensure tests pass with real data

### **For Data Loading:**
```python
def load_real_test_data(num_records=200):
    """Load real DAX market data for testing"""
    import os
    
    # Determine appropriate data file
    if num_records <= 200:
        data_file = os.path.join('tests', 'RealTestData', 'dax_200_bars.csv')
    elif num_records <= 500:
        data_file = os.path.join('tests', 'RealTestData', 'dax_500_bars.csv')
    # ... etc
    
    # Load real data
    data = pd.read_csv(data_file)
    # Process and return real market data
    return data
```

## 🚫 Enforcement

### **Code Review Requirements:**
- All new tests must use real data
- No synthetic data allowed in any form
- Real data usage must be documented

### **Continuous Validation:**
- All tests run with real market data
- No fallback to synthetic data
- Production-ready validation enforced

### **Quality Gates:**
- Tests must pass with real data only
- No exceptions for synthetic data
- Real data compliance is mandatory

---

**🎯 Remember: Real data testing ensures Jaeger is production-ready and reliable for actual trading conditions.**
