import type { Color } from "../core/types";
import type { RGBA } from "../core/util/color";
export declare const YlGn3: number[];
export declare const YlGn4: number[];
export declare const YlGn5: number[];
export declare const YlGn6: number[];
export declare const YlGn7: number[];
export declare const YlGn8: number[];
export declare const YlGn9: number[];
export declare const YlGnBu3: number[];
export declare const YlGnBu4: number[];
export declare const YlGnBu5: number[];
export declare const YlGnBu6: number[];
export declare const YlGnBu7: number[];
export declare const YlGnBu8: number[];
export declare const YlGnBu9: number[];
export declare const GnBu3: number[];
export declare const GnBu4: number[];
export declare const GnBu5: number[];
export declare const GnBu6: number[];
export declare const GnBu7: number[];
export declare const GnBu8: number[];
export declare const GnBu9: number[];
export declare const BuGn3: number[];
export declare const BuGn4: number[];
export declare const BuGn5: number[];
export declare const BuGn6: number[];
export declare const BuGn7: number[];
export declare const BuGn8: number[];
export declare const BuGn9: number[];
export declare const PuBuGn3: number[];
export declare const PuBuGn4: number[];
export declare const PuBuGn5: number[];
export declare const PuBuGn6: number[];
export declare const PuBuGn7: number[];
export declare const PuBuGn8: number[];
export declare const PuBuGn9: number[];
export declare const PuBu3: number[];
export declare const PuBu4: number[];
export declare const PuBu5: number[];
export declare const PuBu6: number[];
export declare const PuBu7: number[];
export declare const PuBu8: number[];
export declare const PuBu9: number[];
export declare const BuPu3: number[];
export declare const BuPu4: number[];
export declare const BuPu5: number[];
export declare const BuPu6: number[];
export declare const BuPu7: number[];
export declare const BuPu8: number[];
export declare const BuPu9: number[];
export declare const RdPu3: number[];
export declare const RdPu4: number[];
export declare const RdPu5: number[];
export declare const RdPu6: number[];
export declare const RdPu7: number[];
export declare const RdPu8: number[];
export declare const RdPu9: number[];
export declare const PuRd3: number[];
export declare const PuRd4: number[];
export declare const PuRd5: number[];
export declare const PuRd6: number[];
export declare const PuRd7: number[];
export declare const PuRd8: number[];
export declare const PuRd9: number[];
export declare const OrRd3: number[];
export declare const OrRd4: number[];
export declare const OrRd5: number[];
export declare const OrRd6: number[];
export declare const OrRd7: number[];
export declare const OrRd8: number[];
export declare const OrRd9: number[];
export declare const YlOrRd3: number[];
export declare const YlOrRd4: number[];
export declare const YlOrRd5: number[];
export declare const YlOrRd6: number[];
export declare const YlOrRd7: number[];
export declare const YlOrRd8: number[];
export declare const YlOrRd9: number[];
export declare const YlOrBr3: number[];
export declare const YlOrBr4: number[];
export declare const YlOrBr5: number[];
export declare const YlOrBr6: number[];
export declare const YlOrBr7: number[];
export declare const YlOrBr8: number[];
export declare const YlOrBr9: number[];
export declare const Purples3: number[];
export declare const Purples4: number[];
export declare const Purples5: number[];
export declare const Purples6: number[];
export declare const Purples7: number[];
export declare const Purples8: number[];
export declare const Purples9: number[];
export declare const Blues3: number[];
export declare const Blues4: number[];
export declare const Blues5: number[];
export declare const Blues6: number[];
export declare const Blues7: number[];
export declare const Blues8: number[];
export declare const Blues9: number[];
export declare const Greens3: number[];
export declare const Greens4: number[];
export declare const Greens5: number[];
export declare const Greens6: number[];
export declare const Greens7: number[];
export declare const Greens8: number[];
export declare const Greens9: number[];
export declare const Oranges3: number[];
export declare const Oranges4: number[];
export declare const Oranges5: number[];
export declare const Oranges6: number[];
export declare const Oranges7: number[];
export declare const Oranges8: number[];
export declare const Oranges9: number[];
export declare const Reds3: number[];
export declare const Reds4: number[];
export declare const Reds5: number[];
export declare const Reds6: number[];
export declare const Reds7: number[];
export declare const Reds8: number[];
export declare const Reds9: number[];
export declare const Greys3: number[];
export declare const Greys4: number[];
export declare const Greys5: number[];
export declare const Greys6: number[];
export declare const Greys7: number[];
export declare const Greys8: number[];
export declare const Greys9: number[];
export declare const Greys10: number[];
export declare const Greys11: number[];
export declare const Greys256: number[];
export declare const PuOr3: number[];
export declare const PuOr4: number[];
export declare const PuOr5: number[];
export declare const PuOr6: number[];
export declare const PuOr7: number[];
export declare const PuOr8: number[];
export declare const PuOr9: number[];
export declare const PuOr10: number[];
export declare const PuOr11: number[];
export declare const BrBG3: number[];
export declare const BrBG4: number[];
export declare const BrBG5: number[];
export declare const BrBG6: number[];
export declare const BrBG7: number[];
export declare const BrBG8: number[];
export declare const BrBG9: number[];
export declare const BrBG10: number[];
export declare const BrBG11: number[];
export declare const PRGn3: number[];
export declare const PRGn4: number[];
export declare const PRGn5: number[];
export declare const PRGn6: number[];
export declare const PRGn7: number[];
export declare const PRGn8: number[];
export declare const PRGn9: number[];
export declare const PRGn10: number[];
export declare const PRGn11: number[];
export declare const PiYG3: number[];
export declare const PiYG4: number[];
export declare const PiYG5: number[];
export declare const PiYG6: number[];
export declare const PiYG7: number[];
export declare const PiYG8: number[];
export declare const PiYG9: number[];
export declare const PiYG10: number[];
export declare const PiYG11: number[];
export declare const RdBu3: number[];
export declare const RdBu4: number[];
export declare const RdBu5: number[];
export declare const RdBu6: number[];
export declare const RdBu7: number[];
export declare const RdBu8: number[];
export declare const RdBu9: number[];
export declare const RdBu10: number[];
export declare const RdBu11: number[];
export declare const RdGy3: number[];
export declare const RdGy4: number[];
export declare const RdGy5: number[];
export declare const RdGy6: number[];
export declare const RdGy7: number[];
export declare const RdGy8: number[];
export declare const RdGy9: number[];
export declare const RdGy10: number[];
export declare const RdGy11: number[];
export declare const RdYlBu3: number[];
export declare const RdYlBu4: number[];
export declare const RdYlBu5: number[];
export declare const RdYlBu6: number[];
export declare const RdYlBu7: number[];
export declare const RdYlBu8: number[];
export declare const RdYlBu9: number[];
export declare const RdYlBu10: number[];
export declare const RdYlBu11: number[];
export declare const Spectral3: number[];
export declare const Spectral4: number[];
export declare const Spectral5: number[];
export declare const Spectral6: number[];
export declare const Spectral7: number[];
export declare const Spectral8: number[];
export declare const Spectral9: number[];
export declare const Spectral10: number[];
export declare const Spectral11: number[];
export declare const RdYlGn3: number[];
export declare const RdYlGn4: number[];
export declare const RdYlGn5: number[];
export declare const RdYlGn6: number[];
export declare const RdYlGn7: number[];
export declare const RdYlGn8: number[];
export declare const RdYlGn9: number[];
export declare const RdYlGn10: number[];
export declare const RdYlGn11: number[];
export declare const Bokeh3: number[];
export declare const Bokeh4: number[];
export declare const Bokeh5: number[];
export declare const Bokeh6: number[];
export declare const Bokeh7: number[];
export declare const Bokeh8: number[];
export declare const Inferno3: number[];
export declare const Inferno4: number[];
export declare const Inferno5: number[];
export declare const Inferno6: number[];
export declare const Inferno7: number[];
export declare const Inferno8: number[];
export declare const Inferno9: number[];
export declare const Inferno10: number[];
export declare const Inferno11: number[];
export declare const Inferno256: number[];
export declare const Magma3: number[];
export declare const Magma4: number[];
export declare const Magma5: number[];
export declare const Magma6: number[];
export declare const Magma7: number[];
export declare const Magma8: number[];
export declare const Magma9: number[];
export declare const Magma10: number[];
export declare const Magma11: number[];
export declare const Magma256: number[];
export declare const Plasma3: number[];
export declare const Plasma4: number[];
export declare const Plasma5: number[];
export declare const Plasma6: number[];
export declare const Plasma7: number[];
export declare const Plasma8: number[];
export declare const Plasma9: number[];
export declare const Plasma10: number[];
export declare const Plasma11: number[];
export declare const Plasma256: number[];
export declare const Viridis3: number[];
export declare const Viridis4: number[];
export declare const Viridis5: number[];
export declare const Viridis6: number[];
export declare const Viridis7: number[];
export declare const Viridis8: number[];
export declare const Viridis9: number[];
export declare const Viridis10: number[];
export declare const Viridis11: number[];
export declare const Viridis256: number[];
export declare const Cividis3: number[];
export declare const Cividis4: number[];
export declare const Cividis5: number[];
export declare const Cividis6: number[];
export declare const Cividis7: number[];
export declare const Cividis8: number[];
export declare const Cividis9: number[];
export declare const Cividis10: number[];
export declare const Cividis11: number[];
export declare const Cividis256: number[];
export declare const Turbo3: number[];
export declare const Turbo4: number[];
export declare const Turbo5: number[];
export declare const Turbo6: number[];
export declare const Turbo7: number[];
export declare const Turbo8: number[];
export declare const Turbo9: number[];
export declare const Turbo10: number[];
export declare const Turbo11: number[];
export declare const Turbo256: number[];
export declare const Accent3: number[];
export declare const Accent4: number[];
export declare const Accent5: number[];
export declare const Accent6: number[];
export declare const Accent7: number[];
export declare const Accent8: number[];
export declare const Dark2_3: number[];
export declare const Dark2_4: number[];
export declare const Dark2_5: number[];
export declare const Dark2_6: number[];
export declare const Dark2_7: number[];
export declare const Dark2_8: number[];
export declare const Paired3: number[];
export declare const Paired4: number[];
export declare const Paired5: number[];
export declare const Paired6: number[];
export declare const Paired7: number[];
export declare const Paired8: number[];
export declare const Paired9: number[];
export declare const Paired10: number[];
export declare const Paired11: number[];
export declare const Paired12: number[];
export declare const Pastel1_3: number[];
export declare const Pastel1_4: number[];
export declare const Pastel1_5: number[];
export declare const Pastel1_6: number[];
export declare const Pastel1_7: number[];
export declare const Pastel1_8: number[];
export declare const Pastel1_9: number[];
export declare const Pastel2_3: number[];
export declare const Pastel2_4: number[];
export declare const Pastel2_5: number[];
export declare const Pastel2_6: number[];
export declare const Pastel2_7: number[];
export declare const Pastel2_8: number[];
export declare const Set1_3: number[];
export declare const Set1_4: number[];
export declare const Set1_5: number[];
export declare const Set1_6: number[];
export declare const Set1_7: number[];
export declare const Set1_8: number[];
export declare const Set1_9: number[];
export declare const Set2_3: number[];
export declare const Set2_4: number[];
export declare const Set2_5: number[];
export declare const Set2_6: number[];
export declare const Set2_7: number[];
export declare const Set2_8: number[];
export declare const Set3_3: number[];
export declare const Set3_4: number[];
export declare const Set3_5: number[];
export declare const Set3_6: number[];
export declare const Set3_7: number[];
export declare const Set3_8: number[];
export declare const Set3_9: number[];
export declare const Set3_10: number[];
export declare const Set3_11: number[];
export declare const Set3_12: number[];
export declare const Category10_3: number[];
export declare const Category10_4: number[];
export declare const Category10_5: number[];
export declare const Category10_6: number[];
export declare const Category10_7: number[];
export declare const Category10_8: number[];
export declare const Category10_9: number[];
export declare const Category10_10: number[];
export declare const Category20_3: number[];
export declare const Category20_4: number[];
export declare const Category20_5: number[];
export declare const Category20_6: number[];
export declare const Category20_7: number[];
export declare const Category20_8: number[];
export declare const Category20_9: number[];
export declare const Category20_10: number[];
export declare const Category20_11: number[];
export declare const Category20_12: number[];
export declare const Category20_13: number[];
export declare const Category20_14: number[];
export declare const Category20_15: number[];
export declare const Category20_16: number[];
export declare const Category20_17: number[];
export declare const Category20_18: number[];
export declare const Category20_19: number[];
export declare const Category20_20: number[];
export declare const Category20b_3: number[];
export declare const Category20b_4: number[];
export declare const Category20b_5: number[];
export declare const Category20b_6: number[];
export declare const Category20b_7: number[];
export declare const Category20b_8: number[];
export declare const Category20b_9: number[];
export declare const Category20b_10: number[];
export declare const Category20b_11: number[];
export declare const Category20b_12: number[];
export declare const Category20b_13: number[];
export declare const Category20b_14: number[];
export declare const Category20b_15: number[];
export declare const Category20b_16: number[];
export declare const Category20b_17: number[];
export declare const Category20b_18: number[];
export declare const Category20b_19: number[];
export declare const Category20b_20: number[];
export declare const Category20c_3: number[];
export declare const Category20c_4: number[];
export declare const Category20c_5: number[];
export declare const Category20c_6: number[];
export declare const Category20c_7: number[];
export declare const Category20c_8: number[];
export declare const Category20c_9: number[];
export declare const Category20c_10: number[];
export declare const Category20c_11: number[];
export declare const Category20c_12: number[];
export declare const Category20c_13: number[];
export declare const Category20c_14: number[];
export declare const Category20c_15: number[];
export declare const Category20c_16: number[];
export declare const Category20c_17: number[];
export declare const Category20c_18: number[];
export declare const Category20c_19: number[];
export declare const Category20c_20: number[];
export declare const Colorblind3: number[];
export declare const Colorblind4: number[];
export declare const Colorblind5: number[];
export declare const Colorblind6: number[];
export declare const Colorblind7: number[];
export declare const Colorblind8: number[];
export declare const Bright3: number[];
export declare const Bright4: number[];
export declare const Bright5: number[];
export declare const Bright6: number[];
export declare const Bright7: number[];
export declare const HighContrast3: number[];
export declare const Vibrant3: number[];
export declare const Vibrant4: number[];
export declare const Vibrant5: number[];
export declare const Vibrant6: number[];
export declare const Vibrant7: number[];
export declare const Muted3: number[];
export declare const Muted4: number[];
export declare const Muted5: number[];
export declare const Muted6: number[];
export declare const Muted7: number[];
export declare const Muted8: number[];
export declare const Muted9: number[];
export declare const MediumContrast3: number[];
export declare const MediumContrast4: number[];
export declare const MediumContrast5: number[];
export declare const MediumContrast6: number[];
export declare const PaleTextBackground: number[];
export declare const DarkText: number[];
export declare const Light3: number[];
export declare const Light4: number[];
export declare const Light5: number[];
export declare const Light6: number[];
export declare const Light7: number[];
export declare const Light8: number[];
export declare const Light9: number[];
export declare const Sunset3: number[];
export declare const Sunset4: number[];
export declare const Sunset5: number[];
export declare const Sunset6: number[];
export declare const Sunset7: number[];
export declare const Sunset8: number[];
export declare const Sunset9: number[];
export declare const Sunset10: number[];
export declare const Sunset11: number[];
export declare const BuRd3: number[];
export declare const BuRd4: number[];
export declare const BuRd5: number[];
export declare const BuRd6: number[];
export declare const BuRd7: number[];
export declare const BuRd8: number[];
export declare const BuRd9: number[];
export declare const TolPRGn3: number[];
export declare const TolPRGn4: number[];
export declare const TolPRGn5: number[];
export declare const TolPRGn6: number[];
export declare const TolPRGn7: number[];
export declare const TolPRGn8: number[];
export declare const TolPRGn9: number[];
export declare const TolYlOrBr3: number[];
export declare const TolYlOrBr4: number[];
export declare const TolYlOrBr5: number[];
export declare const TolYlOrBr6: number[];
export declare const TolYlOrBr7: number[];
export declare const TolYlOrBr8: number[];
export declare const TolYlOrBr9: number[];
export declare const Iridescent3: number[];
export declare const Iridescent4: number[];
export declare const Iridescent5: number[];
export declare const Iridescent6: number[];
export declare const Iridescent7: number[];
export declare const Iridescent8: number[];
export declare const Iridescent9: number[];
export declare const Iridescent10: number[];
export declare const Iridescent11: number[];
export declare const Iridescent12: number[];
export declare const Iridescent13: number[];
export declare const Iridescent14: number[];
export declare const Iridescent15: number[];
export declare const Iridescent16: number[];
export declare const Iridescent17: number[];
export declare const Iridescent18: number[];
export declare const Iridescent19: number[];
export declare const Iridescent20: number[];
export declare const Iridescent21: number[];
export declare const Iridescent22: number[];
export declare const Iridescent23: number[];
export declare const TolRainbow3: number[];
export declare const TolRainbow4: number[];
export declare const TolRainbow5: number[];
export declare const TolRainbow6: number[];
export declare const TolRainbow7: number[];
export declare const TolRainbow8: number[];
export declare const TolRainbow9: number[];
export declare const TolRainbow10: number[];
export declare const TolRainbow11: number[];
export declare const TolRainbow12: number[];
export declare const TolRainbow13: number[];
export declare const TolRainbow14: number[];
export declare const TolRainbow15: number[];
export declare const TolRainbow16: number[];
export declare const TolRainbow17: number[];
export declare const TolRainbow18: number[];
export declare const TolRainbow19: number[];
export declare const TolRainbow20: number[];
export declare const TolRainbow21: number[];
export declare const TolRainbow22: number[];
export declare const TolRainbow23: number[];
export type YlGn = "YlGn3" | "YlGn4" | "YlGn5" | "YlGn6" | "YlGn7" | "YlGn8" | "YlGn9";
export type YlGnBu = "YlGnBu3" | "YlGnBu4" | "YlGnBu5" | "YlGnBu6" | "YlGnBu7" | "YlGnBu8" | "YlGnBu9";
export type GnBu = "GnBu3" | "GnBu4" | "GnBu5" | "GnBu6" | "GnBu7" | "GnBu8" | "GnBu9";
export type BuGn = "BuGn3" | "BuGn4" | "BuGn5" | "BuGn6" | "BuGn7" | "BuGn8" | "BuGn9";
export type PuBuGn = "PuBuGn3" | "PuBuGn4" | "PuBuGn5" | "PuBuGn6" | "PuBuGn7" | "PuBuGn8" | "PuBuGn9";
export type PuBu = "PuBu3" | "PuBu4" | "PuBu5" | "PuBu6" | "PuBu7" | "PuBu8" | "PuBu9";
export type BuPu = "BuPu3" | "BuPu4" | "BuPu5" | "BuPu6" | "BuPu7" | "BuPu8" | "BuPu9";
export type RdPu = "RdPu3" | "RdPu4" | "RdPu5" | "RdPu6" | "RdPu7" | "RdPu8" | "RdPu9";
export type PuRd = "PuRd3" | "PuRd4" | "PuRd5" | "PuRd6" | "PuRd7" | "PuRd8" | "PuRd9";
export type OrRd = "OrRd3" | "OrRd4" | "OrRd5" | "OrRd6" | "OrRd7" | "OrRd8" | "OrRd9";
export type YlOrRd = "YlOrRd3" | "YlOrRd4" | "YlOrRd5" | "YlOrRd6" | "YlOrRd7" | "YlOrRd8" | "YlOrRd9";
export type YlOrBr = "YlOrBr3" | "YlOrBr4" | "YlOrBr5" | "YlOrBr6" | "YlOrBr7" | "YlOrBr8" | "YlOrBr9";
export type Purples = "Purples3" | "Purples4" | "Purples5" | "Purples6" | "Purples7" | "Purples8" | "Purples9";
export type Blues = "Blues3" | "Blues4" | "Blues5" | "Blues6" | "Blues7" | "Blues8" | "Blues9";
export type Greens = "Greens3" | "Greens4" | "Greens5" | "Greens6" | "Greens7" | "Greens8" | "Greens9";
export type Oranges = "Oranges3" | "Oranges4" | "Oranges5" | "Oranges6" | "Oranges7" | "Oranges8" | "Oranges9";
export type Reds = "Reds3" | "Reds4" | "Reds5" | "Reds6" | "Reds7" | "Reds8" | "Reds9";
export type Greys = "Greys3" | "Greys4" | "Greys5" | "Greys6" | "Greys7" | "Greys8" | "Greys9";
export type PuOr = "PuOr3" | "PuOr4" | "PuOr5" | "PuOr6" | "PuOr7" | "PuOr8" | "PuOr9" | "PuOr10" | "PuOr11";
export type BrBG = "BrBG3" | "BrBG4" | "BrBG5" | "BrBG6" | "BrBG7" | "BrBG8" | "BrBG9" | "BrBG10" | "BrBG11";
export type PRGn = "PRGn3" | "PRGn4" | "PRGn5" | "PRGn6" | "PRGn7" | "PRGn8" | "PRGn9" | "PRGn10" | "PRGn11";
export type PiYG = "PiYG3" | "PiYG4" | "PiYG5" | "PiYG6" | "PiYG7" | "PiYG8" | "PiYG9" | "PiYG10" | "PiYG11";
export type RdBu = "RdBu3" | "RdBu4" | "RdBu5" | "RdBu6" | "RdBu7" | "RdBu8" | "RdBu9" | "RdBu10" | "RdBu11";
export type RdGy = "RdGy3" | "RdGy4" | "RdGy5" | "RdGy6" | "RdGy7" | "RdGy8" | "RdGy9" | "RdGy10" | "RdGy11";
export type RdYlBu = "RdYlBu3" | "RdYlBu4" | "RdYlBu5" | "RdYlBu6" | "RdYlBu7" | "RdYlBu8" | "RdYlBu9" | "RdYlBu10" | "RdYlBu11";
export type Spectral = "Spectral3" | "Spectral4" | "Spectral5" | "Spectral6" | "Spectral7" | "Spectral8" | "Spectral9" | "Spectral10" | "Spectral11";
export type RdYlGn = "RdYlGn3" | "RdYlGn4" | "RdYlGn5" | "RdYlGn6" | "RdYlGn7" | "RdYlGn8" | "RdYlGn9" | "RdYlGn10" | "RdYlGn11";
export type Accent = "Accent3" | "Accent4" | "Accent5" | "Accent6" | "Accent7" | "Accent8";
export type Dark2 = "Dark2_3" | "Dark2_4" | "Dark2_5" | "Dark2_6" | "Dark2_7" | "Dark2_8";
export type Paired = "Paired3" | "Paired4" | "Paired5" | "Paired6" | "Paired7" | "Paired8" | "Paired9" | "Paired10" | "Paired11" | "Paired12";
export type Pastel1 = "Pastel1_3" | "Pastel1_4" | "Pastel1_5" | "Pastel1_6" | "Pastel1_7" | "Pastel1_8" | "Pastel1_9";
export type Pastel2 = "Pastel2_3" | "Pastel2_4" | "Pastel2_5" | "Pastel2_6" | "Pastel2_7" | "Pastel2_8";
export type Set1 = "Set1_3" | "Set1_4" | "Set1_5" | "Set1_6" | "Set1_7" | "Set1_8" | "Set1_9";
export type Set2 = "Set2_3" | "Set2_4" | "Set2_5" | "Set2_6" | "Set2_7" | "Set2_8";
export type Set3 = "Set3_3" | "Set3_4" | "Set3_5" | "Set3_6" | "Set3_7" | "Set3_8" | "Set3_9" | "Set3_10" | "Set3_11" | "Set3_12";
export type Bokeh = "Bokeh3" | "Bokeh4" | "Bokeh5" | "Bokeh6" | "Bokeh7" | "Bokeh8";
export type Inferno = "Inferno3" | "Inferno4" | "Inferno5" | "Inferno6" | "Inferno7" | "Inferno8" | "Inferno9" | "Inferno10" | "Inferno11" | "Inferno256";
export type Magma = "Magma3" | "Magma4" | "Magma5" | "Magma6" | "Magma7" | "Magma8" | "Magma9" | "Magma10" | "Magma11" | "Magma256";
export type Plasma = "Plasma3" | "Plasma4" | "Plasma5" | "Plasma6" | "Plasma7" | "Plasma8" | "Plasma9" | "Plasma10" | "Plasma11" | "Plasma256";
export type Viridis = "Viridis3" | "Viridis4" | "Viridis5" | "Viridis6" | "Viridis7" | "Viridis8" | "Viridis9" | "Viridis10" | "Viridis11" | "Viridis256";
export type Cividis = "Cividis3" | "Cividis4" | "Cividis5" | "Cividis6" | "Cividis7" | "Cividis8" | "Cividis9" | "Cividis10" | "Cividis11" | "Cividis256";
export type Category10 = "Category10_3" | "Category10_4" | "Category10_5" | "Category10_6" | "Category10_7" | "Category10_8" | "Category10_9" | "Category10_10";
export type Category20 = "Category20_3" | "Category20_4" | "Category20_5" | "Category20_6" | "Category20_7" | "Category20_8" | "Category20_9" | "Category20_10" | "Category20_11" | "Category20_12" | "Category20_13" | "Category20_14" | "Category20_15" | "Category20_16" | "Category20_17" | "Category20_18" | "Category20_19" | "Category20_20";
export type Category20b = "Category20b_3" | "Category20b_4" | "Category20b_5" | "Category20b_6" | "Category20b_7" | "Category20b_8" | "Category20b_9" | "Category20b_10" | "Category20b_11" | "Category20b_12" | "Category20b_13" | "Category20b_14" | "Category20b_15" | "Category20b_16" | "Category20b_17" | "Category20b_18" | "Category20b_19" | "Category20b_20";
export type Category20c = "Category20c_3" | "Category20c_4" | "Category20c_5" | "Category20c_6" | "Category20c_7" | "Category20c_8" | "Category20c_9" | "Category20c_10" | "Category20c_11" | "Category20c_12" | "Category20c_13" | "Category20c_14" | "Category20c_15" | "Category20c_16" | "Category20c_17" | "Category20c_18" | "Category20c_19" | "Category20c_20";
export type Colorblind = "Colorblind3" | "Colorblind4" | "Colorblind5" | "Colorblind6" | "Colorblind7" | "Colorblind8";
export type Bright = "Bright3" | "Bright4" | "Bright5" | "Bright6" | "Bright7";
export type HighContrast = "HighContrast3";
export type Vibrant = "Vibrant3" | "Vibrant4" | "Vibrant5" | "Vibrant6" | "Vibrant7";
export type Muted = "Muted3" | "Muted4" | "Muted5" | "Muted6" | "Muted7" | "Muted8";
export type MediumContrast = "MediumContrast3" | "MediumContrast4" | "MediumContrast5" | "MediumContrast6";
export type Light = "Light3" | "Light4" | "Light5" | "Light6" | "Light7" | "Light8" | "Light9";
export type Sunset = "Sunset3" | "Sunset4" | "Sunset5" | "Sunset6" | "Sunset7" | "Sunset8" | "Sunset9" | "Sunset10" | "Sunset11";
export type BuRd = "BuRd3" | "BuRd4" | "BuRd5" | "BuRd6" | "BuRd7" | "BuRd8" | "BuRd9";
export type TolPRGn = "TolPRGn3" | "TolPRGn4" | "TolPRGn5" | "TolPRGn6" | "TolPRGn7" | "TolPRGn8" | "TolPRGn9";
export type TolYlOrBr = "TolYlOrBr3" | "TolYlOrBr4" | "TolYlOrBr5" | "TolYlOrBr6" | "TolYlOrBr7" | "TolYlOrBr8" | "TolYlOrBr9";
export type Iridescent = "Iridescent3" | "Iridescent4" | "Iridescent5" | "Iridescent6" | "Iridescent7" | "Iridescent8" | "Iridescent9" | "Iridescent10" | "Iridescent11" | "Iridescent12" | "Iridescent13" | "Iridescent14" | "Iridescent15" | "Iridescent16" | "Iridescent17" | "Iridescent18" | "Iridescent19" | "Iridescent20" | "Iridescent21" | "Iridescent22" | "Iridescent23";
export type TolRainbow = "TolRainbow3" | "TolRainbow4" | "TolRainbow5" | "TolRainbow6" | "TolRainbow7" | "TolRainbow8" | "TolRainbow9" | "TolRainbow10" | "TolRainbow11" | "TolRainbow12" | "TolRainbow13" | "TolRainbow14" | "TolRainbow15" | "TolRainbow16" | "TolRainbow17" | "TolRainbow18" | "TolRainbow19" | "TolRainbow20" | "TolRainbow21" | "TolRainbow22" | "TolRainbow23";
export type Palette = YlGn | YlGnBu | GnBu | BuGn | PuBuGn | PuBu | BuPu | RdPu | PuRd | OrRd | YlOrRd | YlOrBr | Purples | Blues | Greens | Oranges | Reds | Greys | PuOr | BrBG | PRGn | PiYG | RdBu | RdGy | RdYlBu | Spectral | RdYlGn | Accent | Dark2 | Paired | Pastel1 | Pastel2 | Set1 | Set2 | Set3 | Bokeh | Inferno | Magma | Plasma | Viridis | Cividis | Category10 | Category20 | Category20b | Category20c | Colorblind | Bright | HighContrast | Vibrant | Muted | MediumContrast | Light | Sunset | BuRd | TolPRGn | TolYlOrBr | Iridescent | TolRainbow;
export declare const YlGn: {
    YlGn3: number[];
    YlGn4: number[];
    YlGn5: number[];
    YlGn6: number[];
    YlGn7: number[];
    YlGn8: number[];
    YlGn9: number[];
};
export declare const YlGnBu: {
    YlGnBu3: number[];
    YlGnBu4: number[];
    YlGnBu5: number[];
    YlGnBu6: number[];
    YlGnBu7: number[];
    YlGnBu8: number[];
    YlGnBu9: number[];
};
export declare const GnBu: {
    GnBu3: number[];
    GnBu4: number[];
    GnBu5: number[];
    GnBu6: number[];
    GnBu7: number[];
    GnBu8: number[];
    GnBu9: number[];
};
export declare const BuGn: {
    BuGn3: number[];
    BuGn4: number[];
    BuGn5: number[];
    BuGn6: number[];
    BuGn7: number[];
    BuGn8: number[];
    BuGn9: number[];
};
export declare const PuBuGn: {
    PuBuGn3: number[];
    PuBuGn4: number[];
    PuBuGn5: number[];
    PuBuGn6: number[];
    PuBuGn7: number[];
    PuBuGn8: number[];
    PuBuGn9: number[];
};
export declare const PuBu: {
    PuBu3: number[];
    PuBu4: number[];
    PuBu5: number[];
    PuBu6: number[];
    PuBu7: number[];
    PuBu8: number[];
    PuBu9: number[];
};
export declare const BuPu: {
    BuPu3: number[];
    BuPu4: number[];
    BuPu5: number[];
    BuPu6: number[];
    BuPu7: number[];
    BuPu8: number[];
    BuPu9: number[];
};
export declare const RdPu: {
    RdPu3: number[];
    RdPu4: number[];
    RdPu5: number[];
    RdPu6: number[];
    RdPu7: number[];
    RdPu8: number[];
    RdPu9: number[];
};
export declare const PuRd: {
    PuRd3: number[];
    PuRd4: number[];
    PuRd5: number[];
    PuRd6: number[];
    PuRd7: number[];
    PuRd8: number[];
    PuRd9: number[];
};
export declare const OrRd: {
    OrRd3: number[];
    OrRd4: number[];
    OrRd5: number[];
    OrRd6: number[];
    OrRd7: number[];
    OrRd8: number[];
    OrRd9: number[];
};
export declare const YlOrRd: {
    YlOrRd3: number[];
    YlOrRd4: number[];
    YlOrRd5: number[];
    YlOrRd6: number[];
    YlOrRd7: number[];
    YlOrRd8: number[];
    YlOrRd9: number[];
};
export declare const YlOrBr: {
    YlOrBr3: number[];
    YlOrBr4: number[];
    YlOrBr5: number[];
    YlOrBr6: number[];
    YlOrBr7: number[];
    YlOrBr8: number[];
    YlOrBr9: number[];
};
export declare const Purples: {
    Purples3: number[];
    Purples4: number[];
    Purples5: number[];
    Purples6: number[];
    Purples7: number[];
    Purples8: number[];
    Purples9: number[];
};
export declare const Blues: {
    Blues3: number[];
    Blues4: number[];
    Blues5: number[];
    Blues6: number[];
    Blues7: number[];
    Blues8: number[];
    Blues9: number[];
};
export declare const Greens: {
    Greens3: number[];
    Greens4: number[];
    Greens5: number[];
    Greens6: number[];
    Greens7: number[];
    Greens8: number[];
    Greens9: number[];
};
export declare const Oranges: {
    Oranges3: number[];
    Oranges4: number[];
    Oranges5: number[];
    Oranges6: number[];
    Oranges7: number[];
    Oranges8: number[];
    Oranges9: number[];
};
export declare const Reds: {
    Reds3: number[];
    Reds4: number[];
    Reds5: number[];
    Reds6: number[];
    Reds7: number[];
    Reds8: number[];
    Reds9: number[];
};
export declare const Greys: {
    Greys3: number[];
    Greys4: number[];
    Greys5: number[];
    Greys6: number[];
    Greys7: number[];
    Greys8: number[];
    Greys9: number[];
    Greys10: number[];
    Greys11: number[];
    Greys256: number[];
};
export declare const PuOr: {
    PuOr3: number[];
    PuOr4: number[];
    PuOr5: number[];
    PuOr6: number[];
    PuOr7: number[];
    PuOr8: number[];
    PuOr9: number[];
    PuOr10: number[];
    PuOr11: number[];
};
export declare const BrBG: {
    BrBG3: number[];
    BrBG4: number[];
    BrBG5: number[];
    BrBG6: number[];
    BrBG7: number[];
    BrBG8: number[];
    BrBG9: number[];
    BrBG10: number[];
    BrBG11: number[];
};
export declare const PRGn: {
    PRGn3: number[];
    PRGn4: number[];
    PRGn5: number[];
    PRGn6: number[];
    PRGn7: number[];
    PRGn8: number[];
    PRGn9: number[];
    PRGn10: number[];
    PRGn11: number[];
};
export declare const PiYG: {
    PiYG3: number[];
    PiYG4: number[];
    PiYG5: number[];
    PiYG6: number[];
    PiYG7: number[];
    PiYG8: number[];
    PiYG9: number[];
    PiYG10: number[];
    PiYG11: number[];
};
export declare const RdBu: {
    RdBu3: number[];
    RdBu4: number[];
    RdBu5: number[];
    RdBu6: number[];
    RdBu7: number[];
    RdBu8: number[];
    RdBu9: number[];
    RdBu10: number[];
    RdBu11: number[];
};
export declare const RdGy: {
    RdGy3: number[];
    RdGy4: number[];
    RdGy5: number[];
    RdGy6: number[];
    RdGy7: number[];
    RdGy8: number[];
    RdGy9: number[];
    RdGy10: number[];
    RdGy11: number[];
};
export declare const RdYlBu: {
    RdYlBu3: number[];
    RdYlBu4: number[];
    RdYlBu5: number[];
    RdYlBu6: number[];
    RdYlBu7: number[];
    RdYlBu8: number[];
    RdYlBu9: number[];
    RdYlBu10: number[];
    RdYlBu11: number[];
};
export declare const Spectral: {
    Spectral3: number[];
    Spectral4: number[];
    Spectral5: number[];
    Spectral6: number[];
    Spectral7: number[];
    Spectral8: number[];
    Spectral9: number[];
    Spectral10: number[];
    Spectral11: number[];
};
export declare const RdYlGn: {
    RdYlGn3: number[];
    RdYlGn4: number[];
    RdYlGn5: number[];
    RdYlGn6: number[];
    RdYlGn7: number[];
    RdYlGn8: number[];
    RdYlGn9: number[];
    RdYlGn10: number[];
    RdYlGn11: number[];
};
export declare const Bokeh: {
    Bokeh3: number[];
    Bokeh4: number[];
    Bokeh5: number[];
    Bokeh6: number[];
    Bokeh7: number[];
    Bokeh8: number[];
};
export declare const Inferno: {
    Inferno3: number[];
    Inferno4: number[];
    Inferno5: number[];
    Inferno6: number[];
    Inferno7: number[];
    Inferno8: number[];
    Inferno9: number[];
    Inferno10: number[];
    Inferno11: number[];
    Inferno256: number[];
};
export declare const Magma: {
    Magma3: number[];
    Magma4: number[];
    Magma5: number[];
    Magma6: number[];
    Magma7: number[];
    Magma8: number[];
    Magma9: number[];
    Magma10: number[];
    Magma11: number[];
    Magma256: number[];
};
export declare const Plasma: {
    Plasma3: number[];
    Plasma4: number[];
    Plasma5: number[];
    Plasma6: number[];
    Plasma7: number[];
    Plasma8: number[];
    Plasma9: number[];
    Plasma10: number[];
    Plasma11: number[];
    Plasma256: number[];
};
export declare const Viridis: {
    Viridis3: number[];
    Viridis4: number[];
    Viridis5: number[];
    Viridis6: number[];
    Viridis7: number[];
    Viridis8: number[];
    Viridis9: number[];
    Viridis10: number[];
    Viridis11: number[];
    Viridis256: number[];
};
export declare const Cividis: {
    Cividis3: number[];
    Cividis4: number[];
    Cividis5: number[];
    Cividis6: number[];
    Cividis7: number[];
    Cividis8: number[];
    Cividis9: number[];
    Cividis10: number[];
    Cividis11: number[];
    Cividis256: number[];
};
export declare const Turbo: {
    Turbo3: number[];
    Turbo4: number[];
    Turbo5: number[];
    Turbo6: number[];
    Turbo7: number[];
    Turbo8: number[];
    Turbo9: number[];
    Turbo10: number[];
    Turbo11: number[];
    Turbo256: number[];
};
export declare const Accent: {
    Accent3: number[];
    Accent4: number[];
    Accent5: number[];
    Accent6: number[];
    Accent7: number[];
    Accent8: number[];
};
export declare const Dark2: {
    Dark2_3: number[];
    Dark2_4: number[];
    Dark2_5: number[];
    Dark2_6: number[];
    Dark2_7: number[];
    Dark2_8: number[];
};
export declare const Paired: {
    Paired3: number[];
    Paired4: number[];
    Paired5: number[];
    Paired6: number[];
    Paired7: number[];
    Paired8: number[];
    Paired9: number[];
    Paired10: number[];
    Paired11: number[];
    Paired12: number[];
};
export declare const Pastel1: {
    Pastel1_3: number[];
    Pastel1_4: number[];
    Pastel1_5: number[];
    Pastel1_6: number[];
    Pastel1_7: number[];
    Pastel1_8: number[];
    Pastel1_9: number[];
};
export declare const Pastel2: {
    Pastel2_3: number[];
    Pastel2_4: number[];
    Pastel2_5: number[];
    Pastel2_6: number[];
    Pastel2_7: number[];
    Pastel2_8: number[];
};
export declare const Set1: {
    Set1_3: number[];
    Set1_4: number[];
    Set1_5: number[];
    Set1_6: number[];
    Set1_7: number[];
    Set1_8: number[];
    Set1_9: number[];
};
export declare const Set2: {
    Set2_3: number[];
    Set2_4: number[];
    Set2_5: number[];
    Set2_6: number[];
    Set2_7: number[];
    Set2_8: number[];
};
export declare const Set3: {
    Set3_3: number[];
    Set3_4: number[];
    Set3_5: number[];
    Set3_6: number[];
    Set3_7: number[];
    Set3_8: number[];
    Set3_9: number[];
    Set3_10: number[];
    Set3_11: number[];
    Set3_12: number[];
};
export declare const Category10: {
    Category10_3: number[];
    Category10_4: number[];
    Category10_5: number[];
    Category10_6: number[];
    Category10_7: number[];
    Category10_8: number[];
    Category10_9: number[];
    Category10_10: number[];
};
export declare const Category20: {
    Category20_3: number[];
    Category20_4: number[];
    Category20_5: number[];
    Category20_6: number[];
    Category20_7: number[];
    Category20_8: number[];
    Category20_9: number[];
    Category20_10: number[];
    Category20_11: number[];
    Category20_12: number[];
    Category20_13: number[];
    Category20_14: number[];
    Category20_15: number[];
    Category20_16: number[];
    Category20_17: number[];
    Category20_18: number[];
    Category20_19: number[];
    Category20_20: number[];
};
export declare const Category20b: {
    Category20b_3: number[];
    Category20b_4: number[];
    Category20b_5: number[];
    Category20b_6: number[];
    Category20b_7: number[];
    Category20b_8: number[];
    Category20b_9: number[];
    Category20b_10: number[];
    Category20b_11: number[];
    Category20b_12: number[];
    Category20b_13: number[];
    Category20b_14: number[];
    Category20b_15: number[];
    Category20b_16: number[];
    Category20b_17: number[];
    Category20b_18: number[];
    Category20b_19: number[];
    Category20b_20: number[];
};
export declare const Category20c: {
    Category20c_3: number[];
    Category20c_4: number[];
    Category20c_5: number[];
    Category20c_6: number[];
    Category20c_7: number[];
    Category20c_8: number[];
    Category20c_9: number[];
    Category20c_10: number[];
    Category20c_11: number[];
    Category20c_12: number[];
    Category20c_13: number[];
    Category20c_14: number[];
    Category20c_15: number[];
    Category20c_16: number[];
    Category20c_17: number[];
    Category20c_18: number[];
    Category20c_19: number[];
    Category20c_20: number[];
};
export declare const Colorblind: {
    Colorblind3: number[];
    Colorblind4: number[];
    Colorblind5: number[];
    Colorblind6: number[];
    Colorblind7: number[];
    Colorblind8: number[];
};
export declare const Bright: {
    Bright3: number[];
    Bright4: number[];
    Bright5: number[];
    Bright6: number[];
    Bright7: number[];
};
export declare const HighContrast: {
    HighContrast3: number[];
};
export declare const Vibrant: {
    Vibrant3: number[];
    Vibrant4: number[];
    Vibrant5: number[];
    Vibrant6: number[];
    Vibrant7: number[];
};
export declare const Muted: {
    Muted3: number[];
    Muted4: number[];
    Muted5: number[];
    Muted6: number[];
    Muted7: number[];
    Muted8: number[];
};
export declare const MediumContrast: {
    MediumContrast3: number[];
    MediumContrast4: number[];
    MediumContrast5: number[];
    MediumContrast6: number[];
};
export declare const Light: {
    Light3: number[];
    Light4: number[];
    Light5: number[];
    Light6: number[];
    Light7: number[];
    Light8: number[];
    Light9: number[];
};
export declare const Sunset: {
    Sunset3: number[];
    Sunset4: number[];
    Sunset5: number[];
    Sunset6: number[];
    Sunset7: number[];
    Sunset8: number[];
    Sunset9: number[];
    Sunset10: number[];
    Sunset11: number[];
};
export declare const BuRd: {
    BuRd3: number[];
    BuRd4: number[];
    BuRd5: number[];
    BuRd6: number[];
    BuRd7: number[];
    BuRd8: number[];
    BuRd9: number[];
};
export declare const TolPRGn: {
    TolPRGn3: number[];
    TolPRGn4: number[];
    TolPRGn5: number[];
    TolPRGn6: number[];
    TolPRGn7: number[];
    TolPRGn8: number[];
    TolPRGn9: number[];
};
export declare const TolYlOrBr: {
    TolYlOrBr3: number[];
    TolYlOrBr4: number[];
    TolYlOrBr5: number[];
    TolYlOrBr6: number[];
    TolYlOrBr7: number[];
    TolYlOrBr8: number[];
    TolYlOrBr9: number[];
};
export declare const Iridescent: {
    Iridescent3: number[];
    Iridescent4: number[];
    Iridescent5: number[];
    Iridescent6: number[];
    Iridescent7: number[];
    Iridescent8: number[];
    Iridescent9: number[];
    Iridescent10: number[];
    Iridescent11: number[];
    Iridescent12: number[];
    Iridescent13: number[];
    Iridescent14: number[];
    Iridescent15: number[];
    Iridescent16: number[];
    Iridescent17: number[];
    Iridescent18: number[];
    Iridescent19: number[];
    Iridescent20: number[];
    Iridescent21: number[];
    Iridescent22: number[];
    Iridescent23: number[];
};
export declare const TolRainbow: {
    TolRainbow3: number[];
    TolRainbow4: number[];
    TolRainbow5: number[];
    TolRainbow6: number[];
    TolRainbow7: number[];
    TolRainbow8: number[];
    TolRainbow9: number[];
    TolRainbow10: number[];
    TolRainbow11: number[];
    TolRainbow12: number[];
    TolRainbow13: number[];
    TolRainbow14: number[];
    TolRainbow15: number[];
    TolRainbow16: number[];
    TolRainbow17: number[];
    TolRainbow18: number[];
    TolRainbow19: number[];
    TolRainbow20: number[];
    TolRainbow21: number[];
    TolRainbow22: number[];
    TolRainbow23: number[];
};
export declare const brewer: {
    YlGn: {
        YlGn3: number[];
        YlGn4: number[];
        YlGn5: number[];
        YlGn6: number[];
        YlGn7: number[];
        YlGn8: number[];
        YlGn9: number[];
    };
    YlGnBu: {
        YlGnBu3: number[];
        YlGnBu4: number[];
        YlGnBu5: number[];
        YlGnBu6: number[];
        YlGnBu7: number[];
        YlGnBu8: number[];
        YlGnBu9: number[];
    };
    GnBu: {
        GnBu3: number[];
        GnBu4: number[];
        GnBu5: number[];
        GnBu6: number[];
        GnBu7: number[];
        GnBu8: number[];
        GnBu9: number[];
    };
    BuGn: {
        BuGn3: number[];
        BuGn4: number[];
        BuGn5: number[];
        BuGn6: number[];
        BuGn7: number[];
        BuGn8: number[];
        BuGn9: number[];
    };
    PuBuGn: {
        PuBuGn3: number[];
        PuBuGn4: number[];
        PuBuGn5: number[];
        PuBuGn6: number[];
        PuBuGn7: number[];
        PuBuGn8: number[];
        PuBuGn9: number[];
    };
    PuBu: {
        PuBu3: number[];
        PuBu4: number[];
        PuBu5: number[];
        PuBu6: number[];
        PuBu7: number[];
        PuBu8: number[];
        PuBu9: number[];
    };
    BuPu: {
        BuPu3: number[];
        BuPu4: number[];
        BuPu5: number[];
        BuPu6: number[];
        BuPu7: number[];
        BuPu8: number[];
        BuPu9: number[];
    };
    RdPu: {
        RdPu3: number[];
        RdPu4: number[];
        RdPu5: number[];
        RdPu6: number[];
        RdPu7: number[];
        RdPu8: number[];
        RdPu9: number[];
    };
    PuRd: {
        PuRd3: number[];
        PuRd4: number[];
        PuRd5: number[];
        PuRd6: number[];
        PuRd7: number[];
        PuRd8: number[];
        PuRd9: number[];
    };
    OrRd: {
        OrRd3: number[];
        OrRd4: number[];
        OrRd5: number[];
        OrRd6: number[];
        OrRd7: number[];
        OrRd8: number[];
        OrRd9: number[];
    };
    YlOrRd: {
        YlOrRd3: number[];
        YlOrRd4: number[];
        YlOrRd5: number[];
        YlOrRd6: number[];
        YlOrRd7: number[];
        YlOrRd8: number[];
        YlOrRd9: number[];
    };
    YlOrBr: {
        YlOrBr3: number[];
        YlOrBr4: number[];
        YlOrBr5: number[];
        YlOrBr6: number[];
        YlOrBr7: number[];
        YlOrBr8: number[];
        YlOrBr9: number[];
    };
    Purples: {
        Purples3: number[];
        Purples4: number[];
        Purples5: number[];
        Purples6: number[];
        Purples7: number[];
        Purples8: number[];
        Purples9: number[];
    };
    Blues: {
        Blues3: number[];
        Blues4: number[];
        Blues5: number[];
        Blues6: number[];
        Blues7: number[];
        Blues8: number[];
        Blues9: number[];
    };
    Greens: {
        Greens3: number[];
        Greens4: number[];
        Greens5: number[];
        Greens6: number[];
        Greens7: number[];
        Greens8: number[];
        Greens9: number[];
    };
    Oranges: {
        Oranges3: number[];
        Oranges4: number[];
        Oranges5: number[];
        Oranges6: number[];
        Oranges7: number[];
        Oranges8: number[];
        Oranges9: number[];
    };
    Reds: {
        Reds3: number[];
        Reds4: number[];
        Reds5: number[];
        Reds6: number[];
        Reds7: number[];
        Reds8: number[];
        Reds9: number[];
    };
    Greys: {
        Greys3: number[];
        Greys4: number[];
        Greys5: number[];
        Greys6: number[];
        Greys7: number[];
        Greys8: number[];
        Greys9: number[];
        Greys10: number[];
        Greys11: number[];
        Greys256: number[];
    };
    PuOr: {
        PuOr3: number[];
        PuOr4: number[];
        PuOr5: number[];
        PuOr6: number[];
        PuOr7: number[];
        PuOr8: number[];
        PuOr9: number[];
        PuOr10: number[];
        PuOr11: number[];
    };
    BrBG: {
        BrBG3: number[];
        BrBG4: number[];
        BrBG5: number[];
        BrBG6: number[];
        BrBG7: number[];
        BrBG8: number[];
        BrBG9: number[];
        BrBG10: number[];
        BrBG11: number[];
    };
    PRGn: {
        PRGn3: number[];
        PRGn4: number[];
        PRGn5: number[];
        PRGn6: number[];
        PRGn7: number[];
        PRGn8: number[];
        PRGn9: number[];
        PRGn10: number[];
        PRGn11: number[];
    };
    PiYG: {
        PiYG3: number[];
        PiYG4: number[];
        PiYG5: number[];
        PiYG6: number[];
        PiYG7: number[];
        PiYG8: number[];
        PiYG9: number[];
        PiYG10: number[];
        PiYG11: number[];
    };
    RdBu: {
        RdBu3: number[];
        RdBu4: number[];
        RdBu5: number[];
        RdBu6: number[];
        RdBu7: number[];
        RdBu8: number[];
        RdBu9: number[];
        RdBu10: number[];
        RdBu11: number[];
    };
    RdGy: {
        RdGy3: number[];
        RdGy4: number[];
        RdGy5: number[];
        RdGy6: number[];
        RdGy7: number[];
        RdGy8: number[];
        RdGy9: number[];
        RdGy10: number[];
        RdGy11: number[];
    };
    RdYlBu: {
        RdYlBu3: number[];
        RdYlBu4: number[];
        RdYlBu5: number[];
        RdYlBu6: number[];
        RdYlBu7: number[];
        RdYlBu8: number[];
        RdYlBu9: number[];
        RdYlBu10: number[];
        RdYlBu11: number[];
    };
    Spectral: {
        Spectral3: number[];
        Spectral4: number[];
        Spectral5: number[];
        Spectral6: number[];
        Spectral7: number[];
        Spectral8: number[];
        Spectral9: number[];
        Spectral10: number[];
        Spectral11: number[];
    };
    RdYlGn: {
        RdYlGn3: number[];
        RdYlGn4: number[];
        RdYlGn5: number[];
        RdYlGn6: number[];
        RdYlGn7: number[];
        RdYlGn8: number[];
        RdYlGn9: number[];
        RdYlGn10: number[];
        RdYlGn11: number[];
    };
    Accent: {
        Accent3: number[];
        Accent4: number[];
        Accent5: number[];
        Accent6: number[];
        Accent7: number[];
        Accent8: number[];
    };
    Dark2: {
        Dark2_3: number[];
        Dark2_4: number[];
        Dark2_5: number[];
        Dark2_6: number[];
        Dark2_7: number[];
        Dark2_8: number[];
    };
    Paired: {
        Paired3: number[];
        Paired4: number[];
        Paired5: number[];
        Paired6: number[];
        Paired7: number[];
        Paired8: number[];
        Paired9: number[];
        Paired10: number[];
        Paired11: number[];
        Paired12: number[];
    };
    Pastel1: {
        Pastel1_3: number[];
        Pastel1_4: number[];
        Pastel1_5: number[];
        Pastel1_6: number[];
        Pastel1_7: number[];
        Pastel1_8: number[];
        Pastel1_9: number[];
    };
    Pastel2: {
        Pastel2_3: number[];
        Pastel2_4: number[];
        Pastel2_5: number[];
        Pastel2_6: number[];
        Pastel2_7: number[];
        Pastel2_8: number[];
    };
    Set1: {
        Set1_3: number[];
        Set1_4: number[];
        Set1_5: number[];
        Set1_6: number[];
        Set1_7: number[];
        Set1_8: number[];
        Set1_9: number[];
    };
    Set2: {
        Set2_3: number[];
        Set2_4: number[];
        Set2_5: number[];
        Set2_6: number[];
        Set2_7: number[];
        Set2_8: number[];
    };
    Set3: {
        Set3_3: number[];
        Set3_4: number[];
        Set3_5: number[];
        Set3_6: number[];
        Set3_7: number[];
        Set3_8: number[];
        Set3_9: number[];
        Set3_10: number[];
        Set3_11: number[];
        Set3_12: number[];
    };
};
export declare const d3: {
    Category10: {
        Category10_3: number[];
        Category10_4: number[];
        Category10_5: number[];
        Category10_6: number[];
        Category10_7: number[];
        Category10_8: number[];
        Category10_9: number[];
        Category10_10: number[];
    };
    Category20: {
        Category20_3: number[];
        Category20_4: number[];
        Category20_5: number[];
        Category20_6: number[];
        Category20_7: number[];
        Category20_8: number[];
        Category20_9: number[];
        Category20_10: number[];
        Category20_11: number[];
        Category20_12: number[];
        Category20_13: number[];
        Category20_14: number[];
        Category20_15: number[];
        Category20_16: number[];
        Category20_17: number[];
        Category20_18: number[];
        Category20_19: number[];
        Category20_20: number[];
    };
    Category20b: {
        Category20b_3: number[];
        Category20b_4: number[];
        Category20b_5: number[];
        Category20b_6: number[];
        Category20b_7: number[];
        Category20b_8: number[];
        Category20b_9: number[];
        Category20b_10: number[];
        Category20b_11: number[];
        Category20b_12: number[];
        Category20b_13: number[];
        Category20b_14: number[];
        Category20b_15: number[];
        Category20b_16: number[];
        Category20b_17: number[];
        Category20b_18: number[];
        Category20b_19: number[];
        Category20b_20: number[];
    };
    Category20c: {
        Category20c_3: number[];
        Category20c_4: number[];
        Category20c_5: number[];
        Category20c_6: number[];
        Category20c_7: number[];
        Category20c_8: number[];
        Category20c_9: number[];
        Category20c_10: number[];
        Category20c_11: number[];
        Category20c_12: number[];
        Category20c_13: number[];
        Category20c_14: number[];
        Category20c_15: number[];
        Category20c_16: number[];
        Category20c_17: number[];
        Category20c_18: number[];
        Category20c_19: number[];
        Category20c_20: number[];
    };
};
export declare const bokeh: {
    Bokeh: {
        Bokeh3: number[];
        Bokeh4: number[];
        Bokeh5: number[];
        Bokeh6: number[];
        Bokeh7: number[];
        Bokeh8: number[];
    };
};
export declare const mpl: {
    Magma: {
        Magma3: number[];
        Magma4: number[];
        Magma5: number[];
        Magma6: number[];
        Magma7: number[];
        Magma8: number[];
        Magma9: number[];
        Magma10: number[];
        Magma11: number[];
        Magma256: number[];
    };
    Inferno: {
        Inferno3: number[];
        Inferno4: number[];
        Inferno5: number[];
        Inferno6: number[];
        Inferno7: number[];
        Inferno8: number[];
        Inferno9: number[];
        Inferno10: number[];
        Inferno11: number[];
        Inferno256: number[];
    };
    Plasma: {
        Plasma3: number[];
        Plasma4: number[];
        Plasma5: number[];
        Plasma6: number[];
        Plasma7: number[];
        Plasma8: number[];
        Plasma9: number[];
        Plasma10: number[];
        Plasma11: number[];
        Plasma256: number[];
    };
    Viridis: {
        Viridis3: number[];
        Viridis4: number[];
        Viridis5: number[];
        Viridis6: number[];
        Viridis7: number[];
        Viridis8: number[];
        Viridis9: number[];
        Viridis10: number[];
        Viridis11: number[];
        Viridis256: number[];
    };
    Cividis: {
        Cividis3: number[];
        Cividis4: number[];
        Cividis5: number[];
        Cividis6: number[];
        Cividis7: number[];
        Cividis8: number[];
        Cividis9: number[];
        Cividis10: number[];
        Cividis11: number[];
        Cividis256: number[];
    };
    Turbo: {
        Turbo3: number[];
        Turbo4: number[];
        Turbo5: number[];
        Turbo6: number[];
        Turbo7: number[];
        Turbo8: number[];
        Turbo9: number[];
        Turbo10: number[];
        Turbo11: number[];
        Turbo256: number[];
    };
};
export declare const tol: {
    Bright: {
        Bright3: number[];
        Bright4: number[];
        Bright5: number[];
        Bright6: number[];
        Bright7: number[];
    };
    HighContrast: {
        HighContrast3: number[];
    };
    Vibrant: {
        Vibrant3: number[];
        Vibrant4: number[];
        Vibrant5: number[];
        Vibrant6: number[];
        Vibrant7: number[];
    };
    Muted: {
        Muted3: number[];
        Muted4: number[];
        Muted5: number[];
        Muted6: number[];
        Muted7: number[];
        Muted8: number[];
    };
    MediumContrast: {
        MediumContrast3: number[];
        MediumContrast4: number[];
        MediumContrast5: number[];
        MediumContrast6: number[];
    };
    Light: {
        Light3: number[];
        Light4: number[];
        Light5: number[];
        Light6: number[];
        Light7: number[];
        Light8: number[];
        Light9: number[];
    };
    Sunset: {
        Sunset3: number[];
        Sunset4: number[];
        Sunset5: number[];
        Sunset6: number[];
        Sunset7: number[];
        Sunset8: number[];
        Sunset9: number[];
        Sunset10: number[];
        Sunset11: number[];
    };
    BuRd: {
        BuRd3: number[];
        BuRd4: number[];
        BuRd5: number[];
        BuRd6: number[];
        BuRd7: number[];
        BuRd8: number[];
        BuRd9: number[];
    };
    TolPRGn: {
        TolPRGn3: number[];
        TolPRGn4: number[];
        TolPRGn5: number[];
        TolPRGn6: number[];
        TolPRGn7: number[];
        TolPRGn8: number[];
        TolPRGn9: number[];
    };
    TolYlOrBr: {
        TolYlOrBr3: number[];
        TolYlOrBr4: number[];
        TolYlOrBr5: number[];
        TolYlOrBr6: number[];
        TolYlOrBr7: number[];
        TolYlOrBr8: number[];
        TolYlOrBr9: number[];
    };
    Iridescent: {
        Iridescent3: number[];
        Iridescent4: number[];
        Iridescent5: number[];
        Iridescent6: number[];
        Iridescent7: number[];
        Iridescent8: number[];
        Iridescent9: number[];
        Iridescent10: number[];
        Iridescent11: number[];
        Iridescent12: number[];
        Iridescent13: number[];
        Iridescent14: number[];
        Iridescent15: number[];
        Iridescent16: number[];
        Iridescent17: number[];
        Iridescent18: number[];
        Iridescent19: number[];
        Iridescent20: number[];
        Iridescent21: number[];
        Iridescent22: number[];
        Iridescent23: number[];
    };
    TolRainbow: {
        TolRainbow3: number[];
        TolRainbow4: number[];
        TolRainbow5: number[];
        TolRainbow6: number[];
        TolRainbow7: number[];
        TolRainbow8: number[];
        TolRainbow9: number[];
        TolRainbow10: number[];
        TolRainbow11: number[];
        TolRainbow12: number[];
        TolRainbow13: number[];
        TolRainbow14: number[];
        TolRainbow15: number[];
        TolRainbow16: number[];
        TolRainbow17: number[];
        TolRainbow18: number[];
        TolRainbow19: number[];
        TolRainbow20: number[];
        TolRainbow21: number[];
        TolRainbow22: number[];
        TolRainbow23: number[];
    };
};
export declare const colorblind: {
    Colorblind: {
        Colorblind3: number[];
        Colorblind4: number[];
        Colorblind5: number[];
        Colorblind6: number[];
        Colorblind7: number[];
        Colorblind8: number[];
    };
};
export declare function interp_palette(palette: Color[], n: number): RGBA[];
export declare function linear_palette<T>(palette: T[], n: number): T[];
export declare function varying_alpha_palette(color: Color, n?: number | null, start_alpha?: number, end_alpha?: number): string[];
export declare function magma(n: number): number[];
export declare function inferno(n: number): number[];
export declare function plasma(n: number): number[];
export declare function viridis(n: number): number[];
export declare function cividis(n: number): number[];
export declare function turbo(n: number): number[];
export declare function grey(n: number): number[];
/****************************************************************************
 * License regarding the Viridis, Magma, Plasma and Inferno colormaps
 * New matplotlib colormaps by Nathaniel J. Smith, Stefan van der Walt,
 * and (in the case of viridis) Eric Firing.
 *
 * The Viridis, Magma, Plasma, and Inferno color maps are released under the
 * CC0 license / public domain dedication. We would appreciate credit if you
 * use or redistribute these colormaps, but do not impose any legal
 * restrictions.
 *
 * To the extent possible under law, the persons who associated CC0 with
 * mpl-colormaps have waived all copyright and related or neighboring rights
 * to mpl-colormaps.
 *
 * You should have received a copy of the CC0 legalcode along with this
 * work.  If not, see <http://creativecommons.org/publicdomain/zero/1.0/>.
 ****************************************************************************
 * This product includes color specifications and designs developed by
 * Cynthia Brewer (http://colorbrewer2.org/).  The Brewer colormaps are
 * licensed under the Apache v2 license. You may obtain a copy of the
 * License at http://www.apache.org/licenses/LICENSE-2.0
 ****************************************************************************
 * License regarding the D3 color palettes (Category10, Category20,
 * Category20b, and Category 20c):
 *
 * Copyright 2010-2015 Mike Bostock
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * * Redistributions of source code must retain the above copyright notice, this
 *   list of conditions and the following disclaimer.
 *
 * * Redistributions in binary form must reproduce the above copyright notice,
 *   this list of conditions and the following disclaimer in the documentation
 *   and/or other materials provided with the distribution.
 *
 * * Neither the name of the author nor the names of contributors may be used to
 *   endorse or promote products derived from this software without specific
 *   prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
 * CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 ****************************************************************************
 * License regarding Paul Tol's color schemes (Bright, HighContrast, Vibrant,
 * Muted, MediumContrast, PaleTextBackground, DarkText, Light, Sunset, BuRd,
 * TolPRGn, TolYlOrBr, Iridescent, TolRainbow)
 *
 * Copyright (c) 2021, Paul Tol
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * * Redistributions of source code must retain the above copyright notice, this
 *   list of conditions and the following disclaimer.
 *
 * * Redistributions in binary form must reproduce the above copyright notice,
 *   this list of conditions and the following disclaimer in the documentation
 *   and/or other materials provided with the distribution.
 *
 * * Neither the name of the copyright holder nor the names of contributors may
 *   be used to endorse or promote products derived from this software without
 *   specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
 * CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 ****************************************************************************
 */
//# sourceMappingURL=palettes.d.ts.map