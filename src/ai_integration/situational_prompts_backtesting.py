#!/usr/bin/env python3
"""
🎯 BACKTESTING-ONLY SITUATIONAL PROMPTS

Simplified LLM prompts that only target backtesting.py compatibility.
Removes MT4 syntax requirements and dual-format complexity.
"""

class BacktestingOnlyPrompts:
    """Generate simplified prompts for backtesting-only pattern discovery"""
    
    @staticmethod
    def get_core_situational_questions():
        """Core situational analysis questions that guide pattern discovery"""
        return [
            "When this market situation occurs, how do participants typically behave?",
            "What situational contexts create predictable behavioral responses?", 
            "Under what market conditions do statistical edges emerge from participant behavior?",
            "Why do certain market situations create measurable behavioral patterns?",
            "How do different participants react to similar market situations?",
            "What contextual factors create measurable behavioral responses?",
            "When do market inefficiencies emerge from situational dynamics?",
            "Under what conditions do participants behave predictably?"
        ]
    
    @staticmethod
    def get_tom_hougaard_examples():
        """Tom <PERSON>'s ACTUAL situational analysis examples - core to the methodology"""
        return [
            "If Thursday is higher than Friday, then what does the following Monday look like?",
            "Is there evidence to support that what Monday starts, Wednesday continues?",
            "How often is a low or a high made in the first 30 minutes of the day?",
            "How often do gaps occur? Do they always fill, as the saying goes?",
            "How often does the market have trend days? How do you trade a trend day?",
            "Are there common denominators between strong trend days?",
            "Is there support for the comment that strong Fridays means strong Mondays?",
            "If Tuesday is lower than Monday, then what does that mean for Wednesday?",
            "When volatility regime changes, how do participants behave differently?",
            "Under what session conditions do behavioral opportunities emerge?",
            "What participant interactions create statistical edges during specific market contexts?",
            "When do institutional vs retail behaviors create exploitable patterns?"
        ]
    
    @staticmethod
    def generate_backtesting_discovery_prompt(ohlc_data, profit_context="", market_summaries="", performance_feedback=""):
        """
        Generate simplified prompt for backtesting-only pattern discovery
        Removes MT4 complexity and focuses on Python-compatible logic
        """
        
        core_questions = BacktestingOnlyPrompts.get_core_situational_questions()
        tom_examples = BacktestingOnlyPrompts.get_tom_hougaard_examples()
        
        prompt = f"""You are an expert Python trader discovering SIMPLE trading patterns for backtesting analysis.

🎯 GENERATE PATTERNS AS PYTHON-READY LOGIC:
Create patterns using simple Python conditions that execute frequently and can be easily backtested.

💰 TRADING COSTS TO CONSIDER:
- Spread: 1 pip (0.0001) - This is the cost of entering/exiting trades
- Commission: None
- Your patterns MUST account for this 1-pip spread cost to remain profitable
- Ensure profit targets are sufficient to overcome the 1-pip spread

🚨 CRITICAL CONSTRAINT: Patterns must be simple Python logic for backtesting.

EXAMPLE PATTERN FORMAT:
Entry Logic: current_close > previous_high
Stop Logic: previous_low
Target Logic: entry_price + (entry_price - stop_price) * 1.5
Direction: long
Position Size: 1.0

🔍 ENHANCED BEHAVIORAL ANALYSIS:
{market_summaries}

📚 LEARNING FROM PREVIOUS SESSIONS:
{performance_feedback}

🎯 REQUIRED OUTPUT FORMAT - PROFITABLE BACKTESTING PATTERNS:

### PROFITABILITY REQUIREMENT
Only discover patterns that meet profitability criteria:
- Expected win rate >60% OR profit target >2x stop distance
- Clear statistical edge based on market regime analysis
- Simple logic that can be easily validated

### PATTERN DIRECTION GUIDELINES:
- Discover patterns for BOTH directions: LONG and SHORT
- LONG patterns: Entry above previous levels, Stop below entry, Target above entry
- SHORT patterns: Entry below previous levels, Stop above entry, Target below entry
- Ensure stop loss and target are logically consistent with direction

### RULE CLARITY REQUIREMENT

Discover 3-5 PROFITABLE patterns as SIMPLE PYTHON LOGIC:

**PATTERN 1: [Simple Name]**
Market Logic: [Brief explanation why this logic works]
Entry Logic: current_close > previous_high
Direction: long
Stop Logic: previous_low
Target Logic: entry_price + (entry_price - stop_price) * 2.0
Position Size: 1.0
Timeframe: 5min

**PATTERN 2: [Bearish Pattern Name]**
Market Logic: [Brief explanation why this SHORT logic works]
Entry Logic: current_close < previous_low
Direction: short
Stop Logic: previous_high
Target Logic: entry_price - (stop_price - entry_price) * 1.5
Position Size: 1.0
Timeframe: 5min

**PATTERN 3: [Another Pattern]**
[Continue same format for additional patterns...]

CRITICAL REQUIREMENTS:
- Use SITUATIONAL ANALYSIS to discover WHY situations create edges
- Translate situational insights into SIMPLE TRADING RULES for backtesting
- Explain the participant psychology behind each situational edge (WHY)
- Provide clear entry/exit rules for implementation (HOW)
- Keep logic simple: current_price, previous_price, basic comparisons

PATTERN DISCOVERY vs RULE EXECUTION:
- USE behavioral intelligence, regime analysis for PATTERN DISCOVERY
- TRANSLATE sophisticated insights into SIMPLE Python-compatible execution rules
- Behavioral intelligence helps you FIND patterns, but rules must be Python-simple

EXECUTION RULE REQUIREMENTS - BACKTESTING COMPATIBLE:
- ALL RULES MUST BE SIMPLE PYTHON LOGIC
- Use basic price comparisons: current_close > previous_high
- Time filters: Simple hour checks if needed
- Position sizing: Absolute values (1.0, 2.0, etc.) - NO percentages
- Entry conditions: Simple price comparisons
- Stop loss: Previous High/Low or simple calculations
- Exit: Simple profit targets or time-based
- NO complex formulas, NO special syntax, NO complex calculations
- Keep all logic simple and Python-compatible for reliable backtesting

CORE SITUATIONAL QUESTIONS TO EXPLORE:
{chr(10).join([f'- "{q}"' for q in core_questions[:4]])}

SITUATIONAL ANALYSIS EXAMPLES (Tom Hougaard methodology):
{chr(10).join([f'- "{example}"' for example in tom_examples[:6]])}"""

        return prompt

    @staticmethod
    def _analyze_market_regime(ohlc_data):
        """Analyze market regime for intelligent pattern discovery"""
        try:
            # Get price data
            close_col = ohlc_data.get('close', ohlc_data.get('Close', ohlc_data.iloc[:, -2]))

            # Calculate trend
            start_price = close_col.iloc[0]
            end_price = close_col.iloc[-1]
            total_return = ((end_price - start_price) / start_price) * 100

            # Calculate volatility
            returns = close_col.pct_change().dropna()
            volatility = returns.std() * 100

            # Determine regime and bias
            if total_return > 2:
                regime = "STRONG UPTREND"
                bias = "🎯 FOCUS ON LONG PATTERNS - Avoid shorts against strong trend"
                pattern_guidance = "Prioritize breakout patterns, momentum continuation, pullback entries"
            elif total_return > 0.5:
                regime = "UPTRENDING"
                bias = "📈 FAVOR LONG PATTERNS - Be selective with shorts"
                pattern_guidance = "Use trend-following patterns, avoid aggressive counter-trend trades"
            elif total_return < -2:
                regime = "STRONG DOWNTREND"
                bias = "🎯 FOCUS ON SHORT PATTERNS - Avoid longs against strong trend"
                pattern_guidance = "Prioritize breakdown patterns, momentum continuation, bounce shorts"
            elif total_return < -0.5:
                regime = "DOWNTRENDING"
                bias = "📉 FAVOR SHORT PATTERNS - Be selective with longs"
                pattern_guidance = "Use trend-following patterns, avoid aggressive counter-trend trades"
            else:
                regime = "RANGING"
                bias = "⚖️ BALANCED APPROACH - Use both directions with mean reversion focus"
                pattern_guidance = "Focus on range-bound patterns, support/resistance bounces"

            return f"""
📊 Market Regime: {regime} ({total_return:+.2f}% total return)
📊 Volatility Level: {volatility:.2f}% (daily price movement)
{bias}
🧠 Pattern Strategy: {pattern_guidance}

⚠️ CRITICAL: Align your pattern discovery with this regime analysis.
   Patterns that fight the dominant trend will likely be unprofitable."""

        except Exception as e:
            return f"Market regime analysis unavailable: {e}"
