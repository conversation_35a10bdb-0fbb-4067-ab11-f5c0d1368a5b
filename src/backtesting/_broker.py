"""Broker implementation for backtesting framework."""

import warnings
from math import copysign
from typing import List, Optional

import numpy as np
import pandas as pd

from ._util import _Data


class _OutOfMoneyError(Exception):
    pass


class _Broker:
    def __init__(self, *, data, cash, spread, commission, margin, trade_on_close, hedging, exclusive_orders, index):
        assert cash > 0, f"cash should be > 0, is {cash}"
        assert 0 < margin <= 1, f"margin should be between 0 and 1, is {margin}"

        self._data: _Data = data
        self._cash = cash

        if callable(commission):
            self._commission = commission
        else:
            try:
                self._commission_fixed, self._commission_relative = commission
            except TypeError:
                self._commission_fixed, self._commission_relative = 0, commission

            assert self._commission_fixed >= 0, 'Need fixed cash commission in $ >= 0'
            assert -.1 <= self._commission_relative < .1, \
                ("commission should be between -10% "
                 f"(e.g. market-maker's rebates) and 10% (fees), is {self._commission_relative}")
            self._commission = self._commission_func

        self._spread = spread
        self._leverage = 1 / margin
        self._trade_on_close = trade_on_close
        self._hedging = hedging
        self._exclusive_orders = exclusive_orders

        self._equity = np.tile(np.nan, len(index))
        self.orders: List = []
        self.trades: List = []
        self.position = None  # Will be set by Position class
        self.closed_trades: List = []

    def _commission_func(self, order_size, price):
        return self._commission_fixed + abs(order_size) * price * self._commission_relative

    def __repr__(self):
        return f'<Broker: {self._cash:.0f}$ {len(self.trades)} trades>'

    def new_order(self, size: float,
                  limit: Optional[float] = None,
                  stop: Optional[float] = None,
                  sl: Optional[float] = None,
                  tp: Optional[float] = None,
                  tag: object = None, *,
                  trade: Optional = None):
        """
        Argument size indicates whether the order is long or short
        """
        from .backtesting import Order, Trade  # Import here to avoid circular imports
        
        size = float(size)
        stop = stop and float(stop)
        limit = limit and float(limit)
        sl = sl and float(sl)
        tp = tp and float(tp)

        is_long = size > 0
        assert size != 0, size

        adjusted_price = self._adjusted_price(size)

        if is_long:
            if not (sl or -np.inf) < (limit or stop or adjusted_price) < (tp or np.inf):
                raise ValueError(
                    "Long orders require: "
                    f"SL ({sl}) < LIMIT ({limit or stop or adjusted_price}) < TP ({tp})")
        else:
            if not (tp or -np.inf) < (limit or stop or adjusted_price) < (sl or np.inf):
                raise ValueError(
                    "Short orders require: "
                    f"TP ({tp}) < LIMIT ({limit or stop or adjusted_price}) < SL ({sl})")

        order = Order(self, size, limit, stop, sl, tp, trade, tag)

        if not trade:  # If exclusive orders (each new order auto-closes previous orders/position),
            # cancel all non-contingent orders and close all open trades beforehand
            if self._exclusive_orders:
                for o in self.orders:
                    if not o.is_contingent:
                        o.cancel()
                for t in self.trades:
                    t.close()

        # Put the new order in the order queue,
        # Ensure SL orders are processed first
        self.orders.insert(0 if trade and stop else len(self.orders), order)
        return order

    @property
    def last_price(self) -> float:
        """
        Price at the last (current) close.
        """
        return self._data.Close[-1]

    def _adjusted_price(self, size=None, price=None) -> float:
        """
        Long/short `price`, adjusted for spread.

        In long positions, the adjusted price is a fraction higher, and vice versa.
        """
        return (price or self.last_price) * (1 + copysign(self._spread, size))

    @property
    def equity(self) -> float:
        return self._cash + sum(trade.pl for trade in self.trades)

    @property
    def margin_available(self) -> float:
        # From https://github.com/QuantConnect/Lean/pull/3768
        margin_used = sum(trade.value / self._leverage for trade in self.trades)
        return max(0, self.equity - margin_used)

    def next(self):
        i = self._i = len(self._data) - 1
        self._process_orders()

        # Log account equity for the equity curve
        equity = self.equity
        self._equity[i] = equity

        # If equity is negative, set all to 0 and stop the simulation
        if equity <= 0:
            assert self.margin_available <= 0
            for trade in self.trades:
                self._close_trade(trade, self._data.Close[-1], i)
            self._cash = 0
            self._equity[i:] = 0
            raise _OutOfMoneyError

    def _process_orders(self):
        from .backtesting import Trade  # Import here to avoid circular imports
        
        data = self._data
        open, high, low = data.Open[-1], data.High[-1], data.Low[-1]
        reprocess_orders = False

        # Process orders
        for order in list(self.orders):  # type: Order
            # Related SL/TP order was already removed
            if order not in self.orders:
                continue

            # Check if stop condition was hit
            stop_price = order.stop
            if stop_price:
                is_stop_hit = ((high >= stop_price) if order.is_long else (low <= stop_price))
                if not is_stop_hit:
                    continue

                # > When the stop price is reached, a stop order becomes a market/limit order.
                # https://www.sec.gov/fast-answers/answersstopordhtm.html
                order._replace(stop_price=None)

            # Determine purchase price.
            # Check if limit order can be filled.
            if order.limit:
                is_limit_hit = low <= order.limit if order.is_long else high >= order.limit
                # When stop and limit are hit within the same bar, we pessimistically
                # assume limit was hit before the stop (i.e. "before it counts")
                is_limit_hit_before_stop = (is_limit_hit and
                                            (order.limit <= (stop_price or -np.inf) if order.is_long else
                                             order.limit >= (stop_price or np.inf)))
                if not is_limit_hit or is_limit_hit_before_stop:
                    continue

                # stop_price, if set, was hit within this bar
                price = (min(stop_price or open, order.limit) if order.is_long else
                         max(stop_price or open, order.limit))
            else:
                # Market-if-touched / market order
                # Contingent orders always on next open
                prev_close = data.Close[-2]
                price = prev_close if self._trade_on_close and not order.is_contingent else open
                if stop_price:
                    price = max(price, stop_price) if order.is_long else min(price, stop_price)

            # Determine entry/exit bar index
            is_market_order = not order.limit and not stop_price
            time_index = (
                (self._i - 1) if is_market_order and self._trade_on_close and not order.is_contingent
                else self._i)

            # If order is a SL/TP order, it should close an existing trade it was contingent upon
            if order.parent_trade:
                trade = order.parent_trade
                _prev_size = trade.size
                # If order.size is "greater" than trade.size, this order is a trade.close()
                # order and part of the trade was already closed beforehand
                size = copysign(min(abs(_prev_size), abs(order.size)), order.size)

                # If this trade isn't already closed (e.g. on multiple `trade.close(.5)` calls)
                if trade in self.trades:
                    self._reduce_trade(trade, price, size, time_index)
                    assert order.size != -_prev_size or trade not in self.trades

                if order in (trade._sl_order, trade._tp_order):
                    assert order.size == -trade.size
                    assert order not in self.orders  # Removed when trade was closed
                else:
                    # It's a trade.close() order, now done
                    assert abs(_prev_size) >= abs(size) >= 1

                # DEFENSIVE FIX: Check if order exists before removing
                if order in self.orders:
                    self.orders.remove(order)
                continue

            # Else this is a stand-alone trade

            # Adjust price to include commission (or bid-ask spread).
            # In long positions, the adjusted price is a fraction higher, and vice versa.
            adjusted_price = self._adjusted_price(order.size, price)
            adjusted_price_plus_commission = adjusted_price + self._commission(order.size, price)

            # If order size was specified proportionally,
            # precompute true size in units, accounting for margin and spread/commissions
            size = order.size
            if -1 < size < 1:
                size = copysign(int((self.margin_available * self._leverage * abs(size)) //
                                    adjusted_price_plus_commission), size)
                # Not enough cash/margin even for a single unit
                if not size:
                    warnings.warn(
                        f'time={self._i}: Broker canceled the relative-sized '
                        f'order due to insufficient margin.',
                        category=UserWarning)
                    # XXX: The order is canceled by the broker?
                    # DEFENSIVE FIX: Check if order exists before removing
                    if order in self.orders:
                        self.orders.remove(order)
                    continue

            assert size == round(size)
            need_size = int(size)

            if not self._hedging:
                # Fill position by FIFO closing/reducing existing opposite-facing trades.
                # Existing trades are closed at unadjusted price, because the adjustment
                # was already made when buying.
                for trade in list(self.trades):
                    if trade.is_long == order.is_long:
                        continue

                    assert trade.size * order.size < 0

                    # Order size greater than this opposite-directed existing trade,
                    # so it will be closed completely
                    if abs(need_size) >= abs(trade.size):
                        self._close_trade(trade, price, time_index)
                        need_size += trade.size
                    else:
                        # The existing trade is larger than the new order,
                        # so it will only be closed partially
                        self._reduce_trade(trade, price, need_size, time_index)
                        need_size = 0

                    if not need_size:
                        break

            # If we don't have enough liquidity to cover for the order, the broker CANCELS it
            if abs(need_size) * adjusted_price_plus_commission > \
                    self.margin_available * self._leverage:
                # DEFENSIVE FIX: Check if order exists before removing
                if order in self.orders:
                    self.orders.remove(order)
                continue

            # Open a new trade
            if need_size:
                self._open_trade(adjusted_price, need_size, order.sl, order.tp, time_index, order.tag)

                # We need to reprocess the SL/TP orders newly added to the queue.
                # This allows e.g. SL hitting in the same bar the order was open.
                # See https://github.com/kernc/backtesting.py/issues/119
                if order.sl or order.tp:
                    if is_market_order:
                        reprocess_orders = True
                    # Order.stop and TP hit within the same bar, but SL wasn't. This case
                    # is not ambiguous, because stop and TP go in the same price direction.
                    elif (stop_price and not order.limit and order.tp and
                          ((order.is_long and order.tp <= high and (order.sl or -np.inf) < low) or
                           (order.is_short and order.tp >= low and (order.sl or np.inf) > high))):
                        reprocess_orders = True
                    elif (low <= (order.sl or -np.inf) <= high or
                          low <= (order.tp or -np.inf) <= high):
                        warnings.warn(
                            f"({data.index[-1]}) A contingent SL/TP order would execute in the "
                            "same bar its parent stop/limit order was turned into a trade. "
                            "Since we can't assert the precise intra-candle "
                            "price movement, the affected SL/TP order will instead be executed on "
                            "the next (matching) price/bar, making the result (of this trade) "
                            "somewhat dubious. "
                            "See https://github.com/kernc/backtesting.py/issues/119",
                            UserWarning)

            # Order processed
            # DEFENSIVE FIX: Check if order exists before removing
            if order in self.orders:
                self.orders.remove(order)

        if reprocess_orders:
            self._process_orders()

    def _reduce_trade(self, trade, price: float, size: float, time_index: int):
        from .backtesting import Trade  # Import here to avoid circular imports
        
        assert trade.size * size < 0
        assert abs(trade.size) >= abs(size)

        size_left = trade.size + size
        assert size_left * trade.size >= 0

        if not size_left:
            close_trade = trade
        else:
            # Reduce existing trade ...
            trade._replace(size=size_left)
            if trade._sl_order:
                trade._sl_order._replace(size=-trade.size)
            if trade._tp_order:
                trade._tp_order._replace(size=-trade.size)

            # ... by closing a reduced copy of it
            close_trade = trade._copy(size=-size, sl_order=None, tp_order=None)
            self.trades.append(close_trade)

        self._close_trade(close_trade, price, time_index)

    def _close_trade(self, trade, price: float, time_index: int):
        # DEFENSIVE FIX: Check if trade exists before removing to prevent "list.remove(x): x not in list" error
        if trade in self.trades:
            self.trades.remove(trade)
        else:
            # Trade was already removed - this can happen with SL/TP orders
            pass

        # DEFENSIVE FIX: Check if orders exist before removing
        if trade._sl_order and trade._sl_order in self.orders:
            self.orders.remove(trade._sl_order)
        if trade._tp_order and trade._tp_order in self.orders:
            self.orders.remove(trade._tp_order)

        closed_trade = trade._replace(exit_price=price, exit_bar=time_index)
        self.closed_trades.append(closed_trade)

        # Apply commission one more time at trade exit
        commission = self._commission(trade.size, price)
        self._cash += trade.pl - commission

        # Save commissions on Trade instance for stats
        trade_open_commission = self._commission(closed_trade.size, closed_trade.entry_price)
        # applied here instead of on Trade open because size could have changed
        # by way of _reduce_trade()
        closed_trade._commissions = commission + trade_open_commission

    def _open_trade(self, price: float, size: int, sl: Optional[float], tp: Optional[float], time_index: int, tag):
        from .backtesting import Trade  # Import here to avoid circular imports
        
        trade = Trade(self, size, price, time_index, tag)
        self.trades.append(trade)

        # Apply broker commission at trade open
        self._cash -= self._commission(size, price)

        # Create SL/TP (bracket) orders.
        if tp:
            trade.tp = tp
        if sl:
            trade.sl = sl
