#!/usr/bin/env python3
"""
Backtesting Walk-Forward Validator

Integrates walk-forward analysis with backtesting-only approach.
Only profitable patterns advance to MT4 generation.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
import logging
from backtesting import Backtest, Strategy
from walk_forward_tester import WalkForwardTester
from backtesting_rule_parser import BacktestingTradingRule, BacktestingRuleParser

logger = logging.getLogger(__name__)

class BacktestingWalkForwardValidator:
    """Validates backtesting patterns using walk-forward analysis"""
    
    def __init__(self, min_return_threshold: float = 0.0,  # Any positive return
                 min_consistency_score: float = 30.0,  # Lower threshold for testing
                 min_win_rate: float = 30.0,           # Lower threshold for testing
                 max_drawdown_threshold: float = 50.0): # Higher threshold for testing
        """
        Initialize validator with profitability thresholds
        
        Args:
            min_return_threshold: Minimum average return % required
            min_consistency_score: Minimum consistency score required
            min_win_rate: Minimum win rate % required
            max_drawdown_threshold: Maximum drawdown % allowed
        """
        self.min_return_threshold = min_return_threshold
        self.min_consistency_score = min_consistency_score
        self.min_win_rate = min_win_rate
        self.max_drawdown_threshold = max_drawdown_threshold
        
        self.walk_forward_tester = WalkForwardTester(n_splits=2)  # 2 folds for faster validation
        
    def validate_patterns(self, llm_response: str, ohlc_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Validate LLM patterns using walk-forward analysis
        
        Args:
            llm_response: LLM response with backtesting patterns
            ohlc_data: OHLC data for validation
            
        Returns:
            Dict with validation results and profitable patterns
        """
        print("🔄 Starting Walk-Forward Validation...")
        
        try:
            # Parse patterns using backtesting-only parser
            parser = BacktestingRuleParser()
            rules = parser.parse_llm_response(llm_response)
            
            if not rules:
                return {
                    'success': False,
                    'error': 'No patterns parsed from LLM response',
                    'profitable_patterns': [],
                    'validation_results': {}
                }
            
            print(f"📊 Validating {len(rules)} patterns...")
            
            # Validate each pattern individually
            validation_results = {}
            profitable_patterns = []
            
            for rule in rules:
                print(f"   🧪 Testing Pattern {rule.rule_id}: {rule.name}")
                
                # Create strategy for this specific pattern
                pattern_strategy = self._create_pattern_strategy(rule)
                
                # Run walk-forward validation
                wf_results = self._run_walk_forward_validation(
                    pattern_strategy, ohlc_data, rule
                )
                
                # Evaluate profitability
                is_profitable = self._evaluate_profitability(wf_results)
                
                validation_results[rule.rule_id] = {
                    'rule': rule,
                    'walk_forward_results': wf_results,
                    'is_profitable': is_profitable,
                    'metrics': self._extract_key_metrics(wf_results) if wf_results else {}
                }
                
                if is_profitable:
                    profitable_patterns.append(rule)
                    print(f"      ✅ Pattern {rule.rule_id} PASSED validation")
                else:
                    print(f"      ❌ Pattern {rule.rule_id} FAILED validation")
            
            success_rate = len(profitable_patterns) / len(rules) * 100
            print(f"📊 Validation Complete: {len(profitable_patterns)}/{len(rules)} patterns profitable ({success_rate:.1f}%)")
            
            return {
                'success': True,
                'total_patterns': len(rules),
                'profitable_patterns': profitable_patterns,
                'validation_results': validation_results,
                'success_rate': success_rate
            }
            
        except Exception as e:
            logger.error(f"Walk-forward validation failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'profitable_patterns': [],
                'validation_results': {}
            }
    
    def _create_pattern_strategy(self, rule: BacktestingTradingRule):
        """Create a strategy class for a specific pattern"""
        
        class SinglePatternStrategy(Strategy):
            """Strategy that tests a single backtesting pattern"""
            
            def init(self):
                self.rule = rule
                self.parser = BacktestingRuleParser()
                # Create the rule function
                self.rule_function = self.parser._create_python_function(rule)
                
            def next(self):
                if not self.rule_function:
                    return
                
                current_idx = len(self.data.Close) - 1
                if current_idx < 2:
                    return
                
                # Prepare data for rule evaluation
                current_data = pd.DataFrame({
                    'Open': self.data.Open[:current_idx + 1],
                    'High': self.data.High[:current_idx + 1],
                    'Low': self.data.Low[:current_idx + 1],
                    'Close': self.data.Close[:current_idx + 1],
                    'Volume': self.data.Volume[:current_idx + 1] if hasattr(self.data, 'Volume') else [1000] * (current_idx + 1)
                })
                
                # Get signal
                signal = self.rule_function(current_data, current_idx)
                
                if signal:
                    # Execute trade
                    entry_price = signal['entry_price']
                    stop_loss = signal['stop_loss']
                    take_profit = signal['take_profit']
                    direction = signal['direction']
                    position_size = signal['position_size']
                    
                    # Validate signal
                    if direction == 'long':
                        if stop_loss < entry_price < take_profit:
                            self.buy(size=position_size, sl=stop_loss, tp=take_profit)
                    else:  # short
                        if take_profit < entry_price < stop_loss:
                            self.sell(size=position_size, sl=stop_loss, tp=take_profit)
        
        return SinglePatternStrategy
    
    def _run_walk_forward_validation(self, strategy_class, ohlc_data: pd.DataFrame, 
                                   rule: BacktestingTradingRule) -> Optional[Dict[str, Any]]:
        """Run walk-forward validation for a single pattern"""
        try:
            # Ensure minimum data size for walk-forward
            if len(ohlc_data) < 100:
                print(f"      ⚠️ Insufficient data for walk-forward validation ({len(ohlc_data)} bars)")
                return None
            
            # Run walk-forward test
            wf_results = self.walk_forward_tester.run_walk_forward_test(
                data=ohlc_data,
                strategy_class=strategy_class,
                strategy_params={},
                backtest_params={
                    'cash': 10000,
                    'commission': 0.0,
                    'spread': 0.0001,  # 1 pip spread
                    'margin': 1.0,
                    'exclusive_orders': True
                }
            )
            
            return wf_results
            
        except Exception as e:
            print(f"      ❌ Walk-forward validation failed: {e}")
            return None
    
    def _evaluate_profitability(self, wf_results: Optional[Dict[str, Any]]) -> bool:
        """Evaluate if pattern meets profitability criteria"""
        if not wf_results or not wf_results.get('summary'):
            return False
        
        summary = wf_results['summary']
        
        # Check key metrics
        avg_return = summary.get('avg_return', 0)
        consistency_score = summary.get('consistency_score', 0)
        avg_win_rate = summary.get('avg_win_rate', 0)
        max_drawdown = summary.get('max_drawdown', 100)
        
        # Apply thresholds
        meets_return = avg_return >= self.min_return_threshold
        meets_consistency = consistency_score >= self.min_consistency_score
        meets_win_rate = avg_win_rate >= self.min_win_rate
        meets_drawdown = max_drawdown <= self.max_drawdown_threshold
        
        return meets_return and meets_consistency and meets_win_rate and meets_drawdown
    
    def _extract_key_metrics(self, wf_results: Dict[str, Any]) -> Dict[str, float]:
        """Extract key metrics from walk-forward results"""
        if not wf_results or not wf_results.get('summary'):
            return {}
        
        summary = wf_results['summary']
        
        return {
            'avg_return': summary.get('avg_return', 0),
            'consistency_score': summary.get('consistency_score', 0),
            'avg_win_rate': summary.get('avg_win_rate', 0),
            'max_drawdown': summary.get('max_drawdown', 0),
            'total_trades': summary.get('total_trades', 0),
            'avg_sharpe': summary.get('avg_sharpe', 0)
        }
    
    def generate_validation_report(self, validation_results: Dict[str, Any]) -> str:
        """Generate a validation report"""
        if not validation_results.get('success'):
            return f"❌ Validation Failed: {validation_results.get('error', 'Unknown error')}"
        
        total_patterns = validation_results['total_patterns']
        profitable_patterns = validation_results['profitable_patterns']
        success_rate = validation_results['success_rate']
        
        report = f"""# 🔄 Walk-Forward Validation Report

## 📊 Summary
- **Total Patterns Tested**: {total_patterns}
- **Profitable Patterns**: {len(profitable_patterns)}
- **Success Rate**: {success_rate:.1f}%

## 🎯 Profitable Patterns Ready for MT4 Generation:
"""
        
        for pattern in profitable_patterns:
            pattern_results = validation_results['validation_results'][pattern.rule_id]
            metrics = pattern_results['metrics']
            
            report += f"""
### Pattern {pattern.rule_id}: {pattern.name}
- **Average Return**: {metrics.get('avg_return', 0):.2f}%
- **Win Rate**: {metrics.get('avg_win_rate', 0):.1f}%
- **Max Drawdown**: {metrics.get('max_drawdown', 0):.2f}%
- **Total Trades**: {metrics.get('total_trades', 0)}
- **Entry Logic**: {pattern.entry_logic}
- **Direction**: {pattern.direction}
"""
        
        if not profitable_patterns:
            report += "\n❌ No patterns met profitability criteria for MT4 generation."
        
        return report

# Main function for integration
def validate_backtesting_patterns(llm_response: str, ohlc_data: pd.DataFrame) -> Dict[str, Any]:
    """
    Main function to validate backtesting patterns with walk-forward analysis
    
    Args:
        llm_response: LLM response with backtesting patterns
        ohlc_data: OHLC data for validation
        
    Returns:
        Dict with validation results and profitable patterns
    """
    validator = BacktestingWalkForwardValidator()
    return validator.validate_patterns(llm_response, ohlc_data)
