#!/usr/bin/env python3
"""
Jaeger Configuration Settings
Centralized configuration for the LLM Pattern Discovery System
"""

import os
from typing import Dict, Any

class JaegerConfig:
    """Configuration class for Jaeger system"""

    def __init__(self):
        # LM Studio Configuration
        self.LM_STUDIO_URL = os.getenv('LM_STUDIO_URL', 'http://localhost:1234')
        self.LM_STUDIO_TIMEOUT = int(os.getenv('LM_STUDIO_TIMEOUT', '600'))
        self.LLM_TEMPERATURE = float(os.getenv('LLM_TEMPERATURE', '0.7'))  # CRITICAL FIX: Increased to 0.7 to generate much simpler patterns
        self.LLM_MAX_TOKENS = int(os.getenv('LLM_MAX_TOKENS', '4000'))  # Increased for complex behavioral analysis

        # Data Processing Configuration
        self.MIN_RECORDS_REQUIRED = int(os.getenv('MIN_RECORDS_REQUIRED', '100'))
        self.MAX_MEMORY_USAGE_MB = int(os.getenv('MAX_MEMORY_USAGE_MB', '2048'))
        self.DATA_VALIDATION_ENABLED = os.getenv('DATA_VALIDATION_ENABLED', 'true').lower() == 'true'

        # Trading Configuration
        self.DEFAULT_STOP_LOSS_PCT = float(os.getenv('DEFAULT_STOP_LOSS_PCT', '0.8'))
        self.DEFAULT_TAKE_PROFIT_PCT = float(os.getenv('DEFAULT_TAKE_PROFIT_PCT', '1.2'))
        self.MAX_HOLDING_MINUTES = int(os.getenv('MAX_HOLDING_MINUTES', '180'))
        self.MIN_RISK_THRESHOLD = float(os.getenv('MIN_RISK_THRESHOLD', '1e-6'))

        # Backtesting Configuration
        self.RISK_MULTIPLE = float(os.getenv('RISK_MULTIPLE', '3.0'))
        self.MAX_R_MULTIPLE = float(os.getenv('MAX_R_MULTIPLE', '10.0'))
        self.MIN_R_MULTIPLE = float(os.getenv('MIN_R_MULTIPLE', '-10.0'))

        # File Paths - Auto-detect project root and set absolute paths
        # Get the directory where this config file is located (src directory)
        config_dir = os.path.dirname(os.path.abspath(__file__))
        # Get the project root (parent of src directory)
        project_root = os.path.dirname(config_dir)

        self.DATA_DIR = os.getenv('DATA_DIR', os.path.join(project_root, 'data'))
        self.RESULTS_DIR = os.getenv('RESULTS_DIR', os.path.join(project_root, 'results'))
        self.LOG_FILE = os.getenv('LOG_FILE', os.path.join(project_root, 'jaeger.log'))

        # MT4 Configuration
        self.MT4_DEFAULT_LOT_SIZE = float(os.getenv('MT4_DEFAULT_LOT_SIZE', '0.1'))
        self.MT4_MAGIC_NUMBER = int(os.getenv('MT4_MAGIC_NUMBER', '12345'))
        self.MT4_SLIPPAGE = int(os.getenv('MT4_SLIPPAGE', '3'))

        # Logging Configuration
        self.LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
        self.LOG_TO_FILE = os.getenv('LOG_TO_FILE', 'true').lower() == 'true'
        self.LOG_TO_CONSOLE = os.getenv('LOG_TO_CONSOLE', 'true').lower() == 'true'

        # LLM Pattern Discovery Configuration (formerly "Situational Analysis")
        self.SITUATIONAL_ANALYSIS_ENABLED = os.getenv('SITUATIONAL_ANALYSIS_ENABLED', 'true').lower() == 'true'
        self.MIN_SITUATIONAL_SAMPLE_SIZE = int(os.getenv('MIN_SITUATIONAL_SAMPLE_SIZE', '20'))
        self.SITUATIONAL_SIGNIFICANCE_THRESHOLD = float(os.getenv('SITUATIONAL_SIGNIFICANCE_THRESHOLD', '0.05'))
        self.BEHAVIORAL_CONSISTENCY_THRESHOLD = float(os.getenv('BEHAVIORAL_CONSISTENCY_THRESHOLD', '0.1'))
        self.VOLATILITY_REGIME_PERIODS = int(os.getenv('VOLATILITY_REGIME_PERIODS', '20'))
        self.SESSION_TRANSITION_HOURS = os.getenv('SESSION_TRANSITION_HOURS', '0,1,7,8,9,13,14,15,16,21,22').split(',')
        self.ENABLE_TOM_HOUGAARD_ANALYSIS = os.getenv('ENABLE_TOM_HOUGAARD_ANALYSIS', 'true').lower() == 'true'

        # Enhanced Backtesting and Validation Configuration
        self.ENABLE_SITUATIONAL_VALIDATION = os.getenv('ENABLE_SITUATIONAL_VALIDATION', 'true').lower() == 'true'
        self.MIN_CROSS_SITUATIONAL_SAMPLES = int(os.getenv('MIN_CROSS_SITUATIONAL_SAMPLES', '15'))
        self.BEHAVIORAL_STABILITY_PERIODS = int(os.getenv('BEHAVIORAL_STABILITY_PERIODS', '3'))
        self.CONTEXT_ADJUSTED_MIN_TRADES = int(os.getenv('CONTEXT_ADJUSTED_MIN_TRADES', '10'))
        self.VOLATILITY_ADJUSTED_EXPECTATIONS = os.getenv('VOLATILITY_ADJUSTED_EXPECTATIONS', 'true').lower() == 'true'
        self.SESSION_SPECIFIC_VALIDATION = os.getenv('SESSION_SPECIFIC_VALIDATION', 'true').lower() == 'true'
        self.PATTERN_DEGRADATION_THRESHOLD = float(os.getenv('PATTERN_DEGRADATION_THRESHOLD', '0.15'))
        self.SITUATIONAL_CONSISTENCY_THRESHOLD = float(os.getenv('SITUATIONAL_CONSISTENCY_THRESHOLD', '0.7'))

        # Walk-Forward Testing Configuration
        self.ENABLE_WALK_FORWARD_TESTING = os.getenv('ENABLE_WALK_FORWARD_TESTING', 'true').lower() == 'true'
        self.WALK_FORWARD_SPLITS = int(os.getenv('WALK_FORWARD_SPLITS', '5'))
        self.WALK_FORWARD_MIN_CONSISTENCY = float(os.getenv('WALK_FORWARD_MIN_CONSISTENCY', '60.0'))  # 60% consistency threshold

        # v2.0 Validation Configuration
        self.VALIDATION_INITIAL_CASH = float(os.getenv('VALIDATION_INITIAL_CASH', '10000'))
        self.VALIDATION_COMMISSION = float(os.getenv('VALIDATION_COMMISSION', '0.0'))
        self.VALIDATION_SPREAD = float(os.getenv('VALIDATION_SPREAD', '0.0001'))  # 1 pip
        self.VALIDATION_MARGIN = float(os.getenv('VALIDATION_MARGIN', '1.0'))
        self.VALIDATION_MIN_RETURN = float(os.getenv('VALIDATION_MIN_RETURN', '0.0'))
        self.VALIDATION_MIN_CONSISTENCY = float(os.getenv('VALIDATION_MIN_CONSISTENCY', '30.0'))
        self.VALIDATION_MIN_WIN_RATE = float(os.getenv('VALIDATION_MIN_WIN_RATE', '30.0'))
        self.VALIDATION_MAX_DRAWDOWN = float(os.getenv('VALIDATION_MAX_DRAWDOWN', '50.0'))
        self.VALIDATION_MIN_DISTANCE = float(os.getenv('VALIDATION_MIN_DISTANCE', '0.0005'))  # 5 pips minimum

        # TRUE Dynamic Pattern Discovery Configuration
        self.ENABLE_DYNAMIC_CRITERIA = os.getenv('ENABLE_DYNAMIC_CRITERIA', 'true').lower() == 'true'



        # Backtesting Execution Configuration - CENTRALIZED (ALL EDITABLE)
        # LLM provides position sizing - no hardcoded defaults
        self.DEFAULT_INITIAL_CASH = float(os.getenv('DEFAULT_INITIAL_CASH', '100000'))     # $100k
        self.DEFAULT_MARGIN = float(os.getenv('DEFAULT_MARGIN', '0.02'))                  # 2% (50:1 leverage)
        self.DEFAULT_SPREAD = float(os.getenv('DEFAULT_SPREAD', '0.0001'))               # 1 pip spread (0.01% relative to price)
        self.DEFAULT_COMMISSION = float(os.getenv('DEFAULT_COMMISSION', '0.0'))           # No commission
        self.DEFAULT_EXCLUSIVE_ORDERS = os.getenv('DEFAULT_EXCLUSIVE_ORDERS', 'true').lower() == 'true'
        self.DEFAULT_FINALIZE_TRADES = os.getenv('DEFAULT_FINALIZE_TRADES', 'true').lower() == 'true'
        self.DEFAULT_MAX_HOLDING_MINUTES = int(os.getenv('DEFAULT_MAX_HOLDING_MINUTES', '60'))  # 1 hour
        self.DEFAULT_TRADE_ON_CLOSE = os.getenv('DEFAULT_TRADE_ON_CLOSE', 'false').lower() == 'true'
        self.DEFAULT_HEDGING = os.getenv('DEFAULT_HEDGING', 'false').lower() == 'true'

        # Behavioral Intelligence Configuration
        self.SIMPLIFIED_BEHAVIORAL_INTELLIGENCE = os.getenv('SIMPLIFIED_BEHAVIORAL_INTELLIGENCE', 'false').lower() == 'true'

    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            key: value for key, value in self.__dict__.items()
            if not key.startswith('_')
        }

    def update_from_dict(self, config_dict: Dict[str, Any]):
        """Update configuration from dictionary"""
        for key, value in config_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def save_to_file(self, filepath: str = '../jaeger_config.env'):
        """Save configuration to environment file"""
        with open(filepath, 'w') as f:
            f.write("# Jaeger Configuration File\n")
            f.write("# Generated automatically - modify as needed\n\n")

            for key, value in self.to_dict().items():
                f.write(f"{key}={value}\n")

    def load_from_file(self, filepath: str = '../jaeger_config.env'):
        """Load configuration from environment file"""
        if os.path.exists(filepath):
            with open(filepath, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        if '=' in line:
                            key, value = line.split('=', 1)
                            # Convert string values to appropriate types
                            if hasattr(self, key):
                                current_value = getattr(self, key)
                                if isinstance(current_value, bool):
                                    setattr(self, key, value.lower() == 'true')
                                elif isinstance(current_value, int):
                                    setattr(self, key, int(value))
                                elif isinstance(current_value, float):
                                    setattr(self, key, float(value))
                                else:
                                    setattr(self, key, value)

# Global configuration instance
config = JaegerConfig()

# Load configuration from file if it exists (check from src directory)
if os.path.exists('../jaeger_config.env'):
    config.load_from_file()
