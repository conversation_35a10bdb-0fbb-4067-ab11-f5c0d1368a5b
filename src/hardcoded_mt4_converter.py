#!/usr/bin/env python3
"""
Hard-Coded MT4 Converter

Deterministic conversion from validated backtesting rules to MT4 EA code.
No LLM dependency - reliable, predictable conversion.
"""

from typing import List, Dict, Any
from backtesting_rule_parser import BacktestingTradingRule
import re

class HardcodedMT4Converter:
    """Converts validated backtesting rules to MT4 EA code deterministically"""
    
    def __init__(self):
        self.conversion_errors = []
    
    def convert_rules_to_mt4(self, profitable_rules: List[BacktestingTradingRule], 
                           ea_name: str = "J<PERSON>ger_Validated_EA") -> str:
        """
        Convert validated backtesting rules to complete MT4 EA
        
        Args:
            profitable_rules: List of validated profitable rules
            ea_name: Name for the MT4 EA
            
        Returns:
            Complete MT4 EA code string
        """
        if not profitable_rules:
            return self._generate_empty_ea(ea_name)
        
        print(f"🔧 Converting {len(profitable_rules)} validated patterns to MT4...")
        
        # Generate MT4 EA structure
        mt4_code = self._generate_ea_header(ea_name, len(profitable_rules))
        mt4_code += self._generate_input_parameters(profitable_rules)
        mt4_code += self._generate_global_variables()
        mt4_code += self._generate_init_function()
        mt4_code += self._generate_ontick_function(profitable_rules)
        
        # Generate individual rule functions
        for rule in profitable_rules:
            mt4_code += self._generate_rule_function(rule)
        
        mt4_code += self._generate_utility_functions()
        
        print(f"✅ MT4 EA generated: {len(mt4_code)} characters")
        return mt4_code
    
    def _generate_ea_header(self, ea_name: str, num_patterns: int) -> str:
        """Generate MT4 EA header"""
        return f"""//+------------------------------------------------------------------+
//|                                                {ea_name}.mq4 |
//|                        Generated from Validated Backtesting Patterns |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Jaeger Trading System"
#property link      ""
#property version   "1.00"
#property strict

//--- EA Information
// This EA contains {num_patterns} validated patterns that passed walk-forward testing
// Each pattern has been proven profitable through rigorous backtesting

"""
    
    def _generate_input_parameters(self, rules: List[BacktestingTradingRule]) -> str:
        """Generate input parameters section"""
        params = """//+------------------------------------------------------------------+
//| Input Parameters
//+------------------------------------------------------------------+
input double LotSize = 0.1;              // Position size
input int MagicNumber = 12345;           // Magic number for orders
input bool UseTimeFilter = false;        // Enable time filtering
input int StartHour = 9;                 // Trading start hour
input int EndHour = 17;                  // Trading end hour

// Pattern Enable/Disable Controls
"""
        
        for rule in rules:
            params += f"input bool EnablePattern{rule.rule_id} = true;  // Enable {rule.name}\n"
        
        params += "\n"
        return params
    
    def _generate_global_variables(self) -> str:
        """Generate global variables section"""
        return """//+------------------------------------------------------------------+
//| Global Variables
//+------------------------------------------------------------------+
int totalOrders = 0;
datetime lastBarTime = 0;

"""
    
    def _generate_init_function(self) -> str:
        """Generate OnInit function"""
        return """//+------------------------------------------------------------------+
//| Expert initialization function
//+------------------------------------------------------------------+
int OnInit()
{
   Print("Jaeger Validated EA initialized successfully");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print("Jaeger Validated EA deinitialized");
}

"""
    
    def _generate_ontick_function(self, rules: List[BacktestingTradingRule]) -> str:
        """Generate OnTick function with rule calls"""
        ontick = """//+------------------------------------------------------------------+
//| Expert tick function
//+------------------------------------------------------------------+
void OnTick()
{
   // Check for new bar
   if(Time[0] == lastBarTime)
      return;
   lastBarTime = Time[0];
   
   // Apply time filter if enabled
   if(UseTimeFilter && !IsTimeToTrade())
      return;
   
   // Check each validated pattern
"""
        
        for rule in rules:
            ontick += f"   if(EnablePattern{rule.rule_id}) CheckPattern{rule.rule_id}();\n"
        
        ontick += "}\n\n"
        return ontick
    
    def _generate_rule_function(self, rule: BacktestingTradingRule) -> str:
        """Generate MT4 function for a single validated rule"""
        
        # Convert entry logic
        entry_condition = self._convert_entry_logic(rule.entry_logic)
        
        # Convert stop and target logic
        stop_calculation = self._convert_stop_logic(rule.stop_logic, rule.direction)
        target_calculation = self._convert_target_logic(rule.target_logic, rule.direction)
        
        # Determine order type
        order_type = "OP_BUY" if rule.direction == 'long' else "OP_SELL"
        
        function_code = f"""//+------------------------------------------------------------------+
//| Pattern {rule.rule_id}: {rule.name}
//| Validated through walk-forward testing
//+------------------------------------------------------------------+
void CheckPattern{rule.rule_id}()
{{
   // Entry condition: {rule.entry_logic}
   if(!({entry_condition}))
      return;
   
   double entryPrice = {self._get_entry_price(rule.direction)};
   double stopLoss = {stop_calculation};
   double takeProfit = {target_calculation};
   
   // Validate order parameters
   if(!ValidateOrderParams(entryPrice, stopLoss, takeProfit, {order_type}))
      return;
   
   // Calculate position size
   double positionSize = CalculatePositionSize(entryPrice, stopLoss);
   
   // Place order
   int ticket = OrderSend(Symbol(), {order_type}, positionSize, entryPrice, 3, 
                         stopLoss, takeProfit, "Pattern{rule.rule_id}", MagicNumber, 0, clrBlue);
   
   if(ticket > 0)
   {{
      totalOrders++;
      Print("Pattern{rule.rule_id} order placed: ", ticket);
   }}
   else
   {{
      Print("Pattern{rule.rule_id} order failed: ", GetLastError());
   }}
}}

"""
        return function_code
    
    def _convert_entry_logic(self, entry_logic: str) -> str:
        """Convert backtesting entry logic to MT4 syntax"""
        logic = entry_logic.lower().strip()
        
        # Map common patterns
        conversions = {
            'current_close > previous_high': 'Close[0] > High[1]',
            'current_close < previous_low': 'Close[0] < Low[1]',
            'current_close > previous_close': 'Close[0] > Close[1]',
            'current_close < previous_close': 'Close[0] < Close[1]',
            'current_high > previous_high': 'High[0] > High[1]',
            'current_low < previous_low': 'Low[0] < Low[1]'
        }
        
        for pattern, mt4_code in conversions.items():
            if pattern in logic:
                return mt4_code
        
        # Default fallback
        return 'Close[0] > Close[1]'  # Simple momentum
    
    def _convert_stop_logic(self, stop_logic: str, direction: str) -> str:
        """Convert backtesting stop logic to MT4 calculation"""
        logic = stop_logic.lower().strip()
        
        if 'previous_low' in logic:
            return 'Low[1]'
        elif 'previous_high' in logic:
            return 'High[1]'
        elif 'current_low' in logic:
            return 'Low[0]'
        elif 'current_high' in logic:
            return 'High[0]'
        
        # Check for percentage-based stops
        pct_match = re.search(r'(\d+\.?\d*)%', logic)
        if pct_match:
            pct = float(pct_match.group(1)) / 100
            if direction == 'long':
                return f'entryPrice * {1 - pct:.6f}'
            else:
                return f'entryPrice * {1 + pct:.6f}'
        
        # Default stops
        if direction == 'long':
            return 'Low[1]'  # Previous low for long
        else:
            return 'High[1]'  # Previous high for short
    
    def _convert_target_logic(self, target_logic: str, direction: str) -> str:
        """Convert backtesting target logic to MT4 calculation"""
        logic = target_logic.lower().strip()
        
        # Risk-reward based targets
        if 'entry_price + (entry_price - stop_price)' in logic:
            # Extract multiplier
            mult_match = re.search(r'\*\s*(\d+\.?\d*)', logic)
            multiplier = float(mult_match.group(1)) if mult_match else 1.0
            
            if direction == 'long':
                return f'entryPrice + (entryPrice - stopLoss) * {multiplier:.1f}'
            else:
                return f'entryPrice - (stopLoss - entryPrice) * {multiplier:.1f}'
        
        elif 'entry_price - (stop_price - entry_price)' in logic:
            # Extract multiplier
            mult_match = re.search(r'\*\s*(\d+\.?\d*)', logic)
            multiplier = float(mult_match.group(1)) if mult_match else 1.0
            
            return f'entryPrice - (stopLoss - entryPrice) * {multiplier:.1f}'
        
        # Percentage-based targets
        pct_match = re.search(r'(\d+\.?\d*)%', logic)
        if pct_match:
            pct = float(pct_match.group(1)) / 100
            if direction == 'long':
                return f'entryPrice * {1 + pct:.6f}'
            else:
                return f'entryPrice * {1 - pct:.6f}'
        
        # Default 2:1 risk-reward
        if direction == 'long':
            return 'entryPrice + (entryPrice - stopLoss) * 2.0'
        else:
            return 'entryPrice - (stopLoss - entryPrice) * 2.0'
    
    def _get_entry_price(self, direction: str) -> str:
        """Get entry price for direction"""
        if direction == 'long':
            return 'Ask'
        else:
            return 'Bid'
    
    def _generate_utility_functions(self) -> str:
        """Generate utility functions"""
        return """//+------------------------------------------------------------------+
//| Utility Functions
//+------------------------------------------------------------------+
bool IsTimeToTrade()
{
   int currentHour = Hour();
   return (currentHour >= StartHour && currentHour <= EndHour);
}

bool ValidateOrderParams(double entry, double sl, double tp, int orderType)
{
   if(orderType == OP_BUY)
   {
      return (sl < entry && tp > entry && (entry - sl) > Point * 10);
   }
   else if(orderType == OP_SELL)
   {
      return (sl > entry && tp < entry && (sl - entry) > Point * 10);
   }
   return false;
}

double CalculatePositionSize(double entry, double sl)
{
   double riskAmount = MathAbs(entry - sl);
   if(riskAmount == 0) return LotSize;
   
   // Simple fixed lot size for now
   return LotSize;
}
"""
    
    def _generate_empty_ea(self, ea_name: str) -> str:
        """Generate empty EA when no profitable patterns"""
        return f"""//+------------------------------------------------------------------+
//|                                                {ea_name}.mq4 |
//|                        No Profitable Patterns Found |
//+------------------------------------------------------------------+
#property copyright "Jaeger Trading System"
#property version   "1.00"
#property strict

//--- No patterns passed walk-forward validation
//--- This EA will not place any trades

int OnInit()
{{
   Print("No profitable patterns found - EA will not trade");
   return(INIT_SUCCEEDED);
}}

void OnTick()
{{
   // No trading logic - no patterns passed validation
}}
"""

# Main conversion function
def convert_profitable_patterns_to_mt4(profitable_rules: List[BacktestingTradingRule], 
                                     ea_name: str = "Jaeger_Validated_EA") -> str:
    """
    Convert validated profitable patterns to MT4 EA
    
    Args:
        profitable_rules: List of patterns that passed walk-forward validation
        ea_name: Name for the generated EA
        
    Returns:
        Complete MT4 EA code
    """
    converter = HardcodedMT4Converter()
    return converter.convert_rules_to_mt4(profitable_rules, ea_name)
