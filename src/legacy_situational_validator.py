#!/usr/bin/env python3
"""
Situational Pattern Validation System
Implements Phase 2: Statistical Validation Enhancement for situational analysis patterns
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class SituationalValidator:
    """
    Enhanced validation system for situational trading patterns
    Implements context-adjusted performance metrics and cross-situational validation
    """
    
    def __init__(self, config):
        self.config = config
        self.validation_results = {}
        
    def validate_situational_pattern(self, trade_results: List[Dict], 
                                   market_situations: Dict, 
                                   ohlc_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Comprehensive situational pattern validation
        
        Args:
            trade_results: List of trade results from backtesting
            market_situations: Situational context analysis results
            ohlc_data: Original market data
            
        Returns:
            Dict containing validation results and metrics
        """
        if not trade_results:
            return self._create_validation_result(False, "No trades to validate")
            
        validation_result = {
            'is_valid': False,
            'validation_score': 0.0,
            'criteria_met': 0,
            'total_criteria': 7,
            'details': {},
            'warnings': [],
            'recommendations': []
        }
        
        try:
            # Convert trade results to DataFrame for analysis
            trades_df = pd.DataFrame(trade_results)
            
            # 1. Situational Sample Size Validation
            sample_validation = self._validate_situational_sample_size(trades_df, market_situations)
            validation_result['details']['sample_validation'] = sample_validation
            if sample_validation['passed']:
                validation_result['criteria_met'] += 1
                
            # 2. Context-Adjusted Performance Metrics
            performance_validation = self._validate_context_adjusted_performance(trades_df, ohlc_data)
            validation_result['details']['performance_validation'] = performance_validation
            if performance_validation['passed']:
                validation_result['criteria_met'] += 1
                
            # 3. Cross-Situational Consistency
            consistency_validation = self._validate_cross_situational_consistency(trades_df, market_situations)
            validation_result['details']['consistency_validation'] = consistency_validation
            if consistency_validation['passed']:
                validation_result['criteria_met'] += 1
                
            # 4. Behavioral Pattern Stability
            stability_validation = self._validate_behavioral_stability(trades_df, ohlc_data)
            validation_result['details']['stability_validation'] = stability_validation
            if stability_validation['passed']:
                validation_result['criteria_met'] += 1
                
            # 5. Volatility-Adjusted Expectations
            volatility_validation = self._validate_volatility_adjusted_expectations(trades_df, ohlc_data)
            validation_result['details']['volatility_validation'] = volatility_validation
            if volatility_validation['passed']:
                validation_result['criteria_met'] += 1
                
            # 6. Session-Specific Performance
            session_validation = self._validate_session_specific_performance(trades_df)
            validation_result['details']['session_validation'] = session_validation
            if session_validation['passed']:
                validation_result['criteria_met'] += 1
                
            # 7. Pattern Degradation Check
            degradation_validation = self._validate_pattern_degradation(trades_df)
            validation_result['details']['degradation_validation'] = degradation_validation
            if degradation_validation['passed']:
                validation_result['criteria_met'] += 1
                
            # Calculate overall validation score
            validation_result['validation_score'] = validation_result['criteria_met'] / validation_result['total_criteria']
            
            # Pattern is valid if it meets at least 5 out of 7 criteria (71% threshold)
            validation_result['is_valid'] = validation_result['criteria_met'] >= 5
            
            # Generate recommendations
            validation_result['recommendations'] = self._generate_recommendations(validation_result)
            
            logger.info(f"Situational validation complete: {validation_result['criteria_met']}/{validation_result['total_criteria']} criteria met")
            
        except Exception as e:
            logger.error(f"Error in situational validation: {e}")
            validation_result['warnings'].append(f"Validation error: {e}")
            
        return validation_result
    
    def _validate_situational_sample_size(self, trades_df: pd.DataFrame, 
                                        market_situations: Dict) -> Dict[str, Any]:
        """Validate sample size requirements based on situational context"""
        result = {
            'passed': False,
            'score': 0.0,
            'details': {},
            'message': ''
        }
        
        try:
            total_trades = len(trades_df)
            min_required = self.config.MIN_SITUATIONAL_SAMPLE_SIZE
            
            # Adjust minimum sample size based on market situation frequency
            situation_frequency = self._calculate_situation_frequency(market_situations)
            
            if situation_frequency < 0.1:  # Rare situations (< 10% of time)
                adjusted_min = max(10, min_required // 2)
                result['details']['adjustment'] = f"Rare situation: reduced minimum from {min_required} to {adjusted_min}"
            elif situation_frequency > 0.3:  # Common situations (> 30% of time)
                adjusted_min = min_required * 2
                result['details']['adjustment'] = f"Common situation: increased minimum from {min_required} to {adjusted_min}"
            else:
                adjusted_min = min_required
                result['details']['adjustment'] = f"Normal situation: standard minimum {adjusted_min}"
            
            result['details']['total_trades'] = total_trades
            result['details']['required_minimum'] = adjusted_min
            result['details']['situation_frequency'] = situation_frequency
            
            if total_trades >= adjusted_min:
                result['passed'] = True
                result['score'] = min(1.0, total_trades / (adjusted_min * 2))  # Bonus for extra samples
                result['message'] = f"✅ Sufficient sample size: {total_trades} trades (≥{adjusted_min} required)"
            else:
                result['score'] = total_trades / adjusted_min
                result['message'] = f"❌ Insufficient sample size: {total_trades} trades (<{adjusted_min} required)"
                
        except Exception as e:
            result['message'] = f"❌ Sample size validation error: {e}"
            
        return result
    
    def _validate_context_adjusted_performance(self, trades_df: pd.DataFrame, 
                                             ohlc_data: pd.DataFrame) -> Dict[str, Any]:
        """Validate performance metrics adjusted for market context"""
        result = {
            'passed': False,
            'score': 0.0,
            'details': {},
            'message': ''
        }
        
        try:
            # Calculate market volatility context
            market_volatility = ohlc_data['Close'].pct_change().std()
            
            # Calculate basic performance metrics
            r_multiples = trades_df['r_multiple'].values
            avg_r = np.mean(r_multiples)
            win_rate = np.mean(trades_df['win'].values)
            
            # Adjust expectations based on market volatility
            if market_volatility > 0.002:  # High volatility market
                min_expectancy = 0.02  # Lower expectancy acceptable
                min_win_rate = 0.40    # Lower win rate acceptable
                context = "high volatility"
            elif market_volatility < 0.0005:  # Low volatility market
                min_expectancy = 0.08  # Higher expectancy required
                min_win_rate = 0.55    # Higher win rate required
                context = "low volatility"
            else:  # Normal volatility
                min_expectancy = 0.05
                min_win_rate = 0.45
                context = "normal volatility"
            
            result['details']['market_volatility'] = market_volatility
            result['details']['context'] = context
            result['details']['avg_r_multiple'] = avg_r
            result['details']['win_rate'] = win_rate
            result['details']['min_expectancy_required'] = min_expectancy
            result['details']['min_win_rate_required'] = min_win_rate
            
            # Score based on how well it meets context-adjusted criteria
            expectancy_score = min(1.0, max(0.0, avg_r / min_expectancy)) if min_expectancy > 0 else 0
            win_rate_score = min(1.0, max(0.0, win_rate / min_win_rate)) if min_win_rate > 0 else 0
            
            result['score'] = (expectancy_score + win_rate_score) / 2
            
            if avg_r >= min_expectancy and win_rate >= min_win_rate:
                result['passed'] = True
                result['message'] = f"✅ Context-adjusted performance met ({context}): {avg_r:.3f} expectancy, {win_rate:.1%} win rate"
            else:
                result['message'] = f"❌ Context-adjusted performance failed ({context}): {avg_r:.3f} expectancy, {win_rate:.1%} win rate"
                
        except Exception as e:
            result['message'] = f"❌ Context performance validation error: {e}"
            
        return result
    
    def _calculate_situation_frequency(self, market_situations: Dict) -> float:
        """Calculate how frequently the market situation occurs"""
        try:
            total_samples = 0
            situation_samples = 0
            
            for category, situations in market_situations.items():
                if isinstance(situations, dict):
                    for situation_name, situation_data in situations.items():
                        if isinstance(situation_data, dict) and 'sample_size' in situation_data:
                            situation_samples += situation_data['sample_size']
                            total_samples += situation_data.get('total_periods', situation_data['sample_size'] * 2)
            
            if total_samples > 0:
                return situation_samples / total_samples
            else:
                return 0.5  # Default assumption if can't calculate
                
        except Exception as e:
            logger.warning(f"Error calculating situation frequency: {e}")
            return 0.5  # Default assumption
    
    def _validate_cross_situational_consistency(self, trades_df: pd.DataFrame,
                                              market_situations: Dict) -> Dict[str, Any]:
        """Validate that patterns are consistent across similar market situations"""
        result = {
            'passed': False,
            'score': 0.0,
            'details': {},
            'message': ''
        }

        try:
            # Group trades by time periods to check consistency
            trades_df = trades_df.copy()  # Avoid SettingWithCopyWarning
            trades_df['entry_hour'] = pd.to_datetime(trades_df['entry_time']).dt.hour

            # Check consistency across different hours
            hourly_performance = {}
            for hour in trades_df['entry_hour'].unique():
                hour_trades = trades_df[trades_df['entry_hour'] == hour]
                if len(hour_trades) >= 3:  # Minimum trades per hour
                    hourly_performance[hour] = {
                        'avg_r': hour_trades['r_multiple'].mean(),
                        'win_rate': hour_trades['win'].mean(),
                        'trade_count': len(hour_trades)
                    }

            if len(hourly_performance) >= 2:
                # Calculate consistency score based on variance in performance
                avg_r_values = [perf['avg_r'] for perf in hourly_performance.values()]
                win_rate_values = [perf['win_rate'] for perf in hourly_performance.values()]

                r_consistency = 1.0 - (np.std(avg_r_values) / (np.mean(avg_r_values) + 0.01))
                win_rate_consistency = 1.0 - np.std(win_rate_values)

                result['score'] = (r_consistency + win_rate_consistency) / 2
                result['details']['hourly_performance'] = hourly_performance
                result['details']['r_consistency'] = r_consistency
                result['details']['win_rate_consistency'] = win_rate_consistency

                if result['score'] >= self.config.SITUATIONAL_CONSISTENCY_THRESHOLD:
                    result['passed'] = True
                    result['message'] = f"✅ Cross-situational consistency: {result['score']:.2f} score"
                else:
                    result['message'] = f"❌ Poor cross-situational consistency: {result['score']:.2f} score"
            else:
                result['message'] = "⚠️ Insufficient data for cross-situational analysis"
                result['score'] = 0.5  # Neutral score

        except Exception as e:
            result['message'] = f"❌ Cross-situational validation error: {e}"

        return result

    def _validate_behavioral_stability(self, trades_df: pd.DataFrame,
                                     ohlc_data: pd.DataFrame) -> Dict[str, Any]:
        """Validate that behavioral patterns remain stable over time"""
        result = {
            'passed': False,
            'score': 0.0,
            'details': {},
            'message': ''
        }

        try:
            # Split trades into time periods to check stability
            trades_df = trades_df.copy()  # Avoid SettingWithCopyWarning
            trades_df['entry_date'] = pd.to_datetime(trades_df['entry_time']).dt.date
            date_range = trades_df['entry_date'].max() - trades_df['entry_date'].min()

            if date_range.days < 7:
                result['message'] = "⚠️ Insufficient time range for stability analysis"
                result['score'] = 0.7  # Assume stable for short periods
                result['passed'] = True
                return result

            # Split into periods
            periods = self.config.BEHAVIORAL_STABILITY_PERIODS
            period_length = date_range.days // periods

            period_performance = []
            for i in range(periods):
                start_date = trades_df['entry_date'].min() + timedelta(days=i * period_length)
                end_date = start_date + timedelta(days=period_length)

                period_trades = trades_df[
                    (trades_df['entry_date'] >= start_date) &
                    (trades_df['entry_date'] < end_date)
                ]

                if len(period_trades) >= 3:
                    period_performance.append({
                        'period': i + 1,
                        'avg_r': period_trades['r_multiple'].mean(),
                        'win_rate': period_trades['win'].mean(),
                        'trade_count': len(period_trades),
                        'start_date': start_date,
                        'end_date': end_date
                    })

            if len(period_performance) >= 2:
                # Check for degradation over time
                avg_r_trend = [p['avg_r'] for p in period_performance]
                win_rate_trend = [p['win_rate'] for p in period_performance]

                # Calculate trend (negative slope indicates degradation)
                periods_x = list(range(len(avg_r_trend)))
                r_slope = np.polyfit(periods_x, avg_r_trend, 1)[0] if len(avg_r_trend) > 1 else 0
                win_rate_slope = np.polyfit(periods_x, win_rate_trend, 1)[0] if len(win_rate_trend) > 1 else 0

                # Check if degradation exceeds threshold
                degradation_threshold = self.config.PATTERN_DEGRADATION_THRESHOLD
                r_degradation = abs(r_slope) if r_slope < 0 else 0
                win_rate_degradation = abs(win_rate_slope) if win_rate_slope < 0 else 0

                result['details']['period_performance'] = period_performance
                result['details']['r_slope'] = r_slope
                result['details']['win_rate_slope'] = win_rate_slope
                result['details']['r_degradation'] = r_degradation
                result['details']['win_rate_degradation'] = win_rate_degradation

                if r_degradation <= degradation_threshold and win_rate_degradation <= degradation_threshold:
                    result['passed'] = True
                    result['score'] = 1.0 - max(r_degradation, win_rate_degradation) / degradation_threshold
                    result['message'] = f"✅ Pattern stability maintained: {result['score']:.2f} score"
                else:
                    result['score'] = max(0.0, 1.0 - max(r_degradation, win_rate_degradation) / degradation_threshold)
                    result['message'] = f"❌ Pattern degradation detected: {max(r_degradation, win_rate_degradation):.3f}"
            else:
                result['message'] = "⚠️ Insufficient periods for stability analysis"
                result['score'] = 0.6

        except Exception as e:
            result['message'] = f"❌ Behavioral stability validation error: {e}"

        return result

    def _validate_volatility_adjusted_expectations(self, trades_df: pd.DataFrame,
                                                 ohlc_data: pd.DataFrame) -> Dict[str, Any]:
        """Validate performance expectations adjusted for market volatility"""
        result = {
            'passed': False,
            'score': 0.0,
            'details': {},
            'message': ''
        }

        try:
            if not self.config.VOLATILITY_ADJUSTED_EXPECTATIONS:
                result['passed'] = True
                result['score'] = 1.0
                result['message'] = "✅ Volatility adjustment disabled - validation passed"
                return result

            # Calculate market volatility during trade periods
            trade_volatilities = []
            for _, trade in trades_df.iterrows():
                entry_time = pd.to_datetime(trade['entry_time'])
                # Get volatility around trade entry (±5 periods)
                trade_data = ohlc_data[
                    (ohlc_data['datetime'] >= entry_time - timedelta(minutes=5)) &
                    (ohlc_data['datetime'] <= entry_time + timedelta(minutes=5))
                ]
                if len(trade_data) > 1:
                    vol = trade_data['Close'].pct_change().std()
                    trade_volatilities.append(vol)

            if trade_volatilities:
                avg_trade_volatility = np.mean(trade_volatilities)
                market_volatility = ohlc_data['Close'].pct_change().std()

                # Adjust expectations based on volatility ratio
                volatility_ratio = avg_trade_volatility / market_volatility if market_volatility > 0 else 1.0

                # Higher volatility should allow for higher R multiples
                expected_r_adjustment = volatility_ratio * 0.5  # Scale factor
                actual_avg_r = trades_df['r_multiple'].mean()

                result['details']['avg_trade_volatility'] = avg_trade_volatility
                result['details']['market_volatility'] = market_volatility
                result['details']['volatility_ratio'] = volatility_ratio
                result['details']['expected_r_adjustment'] = expected_r_adjustment
                result['details']['actual_avg_r'] = actual_avg_r

                # Score based on how well actual performance matches volatility-adjusted expectations
                if volatility_ratio > 1.2:  # High volatility trades
                    if actual_avg_r >= expected_r_adjustment:
                        result['passed'] = True
                        result['score'] = min(1.0, actual_avg_r / expected_r_adjustment)
                        result['message'] = f"✅ Volatility-adjusted performance met: {actual_avg_r:.3f} R (expected ≥{expected_r_adjustment:.3f})"
                    else:
                        result['score'] = actual_avg_r / expected_r_adjustment if expected_r_adjustment > 0 else 0
                        result['message'] = f"❌ Volatility-adjusted performance failed: {actual_avg_r:.3f} R (expected ≥{expected_r_adjustment:.3f})"
                else:  # Normal/low volatility trades
                    result['passed'] = True
                    result['score'] = 1.0
                    result['message'] = f"✅ Normal volatility trades: {actual_avg_r:.3f} R"
            else:
                result['message'] = "⚠️ Could not calculate trade volatilities"
                result['score'] = 0.5

        except Exception as e:
            result['message'] = f"❌ Volatility validation error: {e}"

        return result

    def _validate_session_specific_performance(self, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """Validate performance across different trading sessions"""
        result = {
            'passed': False,
            'score': 0.0,
            'details': {},
            'message': ''
        }

        try:
            if not self.config.SESSION_SPECIFIC_VALIDATION:
                result['passed'] = True
                result['score'] = 1.0
                result['message'] = "✅ Session validation disabled - validation passed"
                return result

            # Define trading sessions
            sessions = {
                'Asian': (0, 8),      # 00:00 - 08:00
                'European': (8, 16),   # 08:00 - 16:00
                'American': (16, 24)   # 16:00 - 24:00
            }

            trades_df = trades_df.copy()  # Avoid SettingWithCopyWarning
            trades_df['entry_hour'] = pd.to_datetime(trades_df['entry_time']).dt.hour
            session_performance = {}

            for session_name, (start_hour, end_hour) in sessions.items():
                session_trades = trades_df[
                    (trades_df['entry_hour'] >= start_hour) &
                    (trades_df['entry_hour'] < end_hour)
                ]

                if len(session_trades) >= 3:
                    session_performance[session_name] = {
                        'avg_r': session_trades['r_multiple'].mean(),
                        'win_rate': session_trades['win'].mean(),
                        'trade_count': len(session_trades),
                        'profit_factor': self._calculate_profit_factor(session_trades['r_multiple'].values)
                    }

            result['details']['session_performance'] = session_performance

            if len(session_performance) >= 1:
                # Check if at least one session shows good performance
                good_sessions = 0
                total_sessions = len(session_performance)

                for session_name, perf in session_performance.items():
                    if perf['avg_r'] > 0 and perf['win_rate'] > 0.4:
                        good_sessions += 1

                result['score'] = good_sessions / total_sessions if total_sessions > 0 else 0

                if good_sessions >= 1:
                    result['passed'] = True
                    result['message'] = f"✅ Session performance: {good_sessions}/{total_sessions} sessions profitable"
                else:
                    result['message'] = f"❌ Session performance: {good_sessions}/{total_sessions} sessions profitable"
            else:
                result['message'] = "⚠️ Insufficient data for session analysis"
                result['score'] = 0.5

        except Exception as e:
            result['message'] = f"❌ Session validation error: {e}"

        return result

    def _validate_pattern_degradation(self, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """Check for pattern degradation over time"""
        result = {
            'passed': False,
            'score': 0.0,
            'details': {},
            'message': ''
        }

        try:
            # Sort trades by entry time
            trades_df_sorted = trades_df.sort_values('entry_time')

            if len(trades_df_sorted) < 10:
                result['passed'] = True
                result['score'] = 1.0
                result['message'] = "✅ Insufficient trades for degradation analysis - assumed stable"
                return result

            # Split into first half and second half
            mid_point = len(trades_df_sorted) // 2
            first_half = trades_df_sorted.iloc[:mid_point]
            second_half = trades_df_sorted.iloc[mid_point:]

            first_half_r = first_half['r_multiple'].mean()
            second_half_r = second_half['r_multiple'].mean()
            first_half_wr = first_half['win'].mean()
            second_half_wr = second_half['win'].mean()

            # Calculate degradation
            r_degradation = first_half_r - second_half_r if first_half_r > second_half_r else 0
            wr_degradation = first_half_wr - second_half_wr if first_half_wr > second_half_wr else 0

            max_degradation = max(r_degradation, wr_degradation)

            result['details']['first_half_r'] = first_half_r
            result['details']['second_half_r'] = second_half_r
            result['details']['first_half_wr'] = first_half_wr
            result['details']['second_half_wr'] = second_half_wr
            result['details']['r_degradation'] = r_degradation
            result['details']['wr_degradation'] = wr_degradation
            result['details']['max_degradation'] = max_degradation

            if max_degradation <= self.config.PATTERN_DEGRADATION_THRESHOLD:
                result['passed'] = True
                result['score'] = 1.0 - (max_degradation / self.config.PATTERN_DEGRADATION_THRESHOLD)
                result['message'] = f"✅ Pattern stability maintained: {max_degradation:.3f} degradation"
            else:
                result['score'] = max(0.0, 1.0 - (max_degradation / self.config.PATTERN_DEGRADATION_THRESHOLD))
                result['message'] = f"❌ Pattern degradation detected: {max_degradation:.3f} degradation"

        except Exception as e:
            result['message'] = f"❌ Degradation validation error: {e}"

        return result

    def _calculate_profit_factor(self, r_multiples: np.ndarray) -> float:
        """Calculate profit factor from R multiples"""
        try:
            winning_r = np.sum(r_multiples[r_multiples > 0])
            losing_r = abs(np.sum(r_multiples[r_multiples < 0]))

            if losing_r > 0:
                return winning_r / losing_r
            elif winning_r > 0:
                return float('inf')
            else:
                return 0.0
        except:
            return 0.0

    def _generate_recommendations(self, validation_result: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on validation results"""
        recommendations = []

        try:
            score = validation_result['validation_score']
            criteria_met = validation_result['criteria_met']

            if score < 0.5:
                recommendations.append("Pattern shows poor overall validation - consider rejecting")
            elif score < 0.7:
                recommendations.append("Pattern shows marginal validation - use with caution")
            else:
                recommendations.append("Pattern shows strong validation - suitable for trading")

            # Specific recommendations based on failed criteria
            details = validation_result.get('details', {})

            if 'sample_validation' in details and not details['sample_validation']['passed']:
                recommendations.append("Increase sample size by collecting more data or relaxing entry criteria")

            if 'performance_validation' in details and not details['performance_validation']['passed']:
                recommendations.append("Improve pattern selectivity or adjust risk management parameters")

            if 'consistency_validation' in details and not details['consistency_validation']['passed']:
                recommendations.append("Pattern may be time-specific - consider session filters")

            if 'stability_validation' in details and not details['stability_validation']['passed']:
                recommendations.append("Pattern may be degrading - monitor closely or retrain")

        except Exception as e:
            recommendations.append(f"Error generating recommendations: {e}")

        return recommendations

    def _create_validation_result(self, is_valid: bool, message: str) -> Dict[str, Any]:
        """Create a basic validation result structure"""
        return {
            'is_valid': is_valid,
            'validation_score': 1.0 if is_valid else 0.0,
            'criteria_met': 1 if is_valid else 0,
            'total_criteria': 1,
            'details': {},
            'warnings': [],
            'recommendations': [],
            'message': message
        }
