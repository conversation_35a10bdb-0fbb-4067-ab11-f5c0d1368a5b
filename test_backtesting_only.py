#!/usr/bin/env python3
"""
Test script for backtesting-only pattern generation
Verifies that the new approach improves parsing success rates
"""

import sys
import os
sys.path.append('src')

from backtesting_rule_parser import BacktestingRuleParser, parse_backtesting_rules
from ai_integration.situational_prompts_backtesting import BacktestingOnlyPrompts
import pandas as pd

def test_backtesting_parser():
    """Test the new backtesting-only parser"""
    print("🧪 Testing Backtesting-Only Parser...")
    
    # Sample LLM response in new format
    sample_response = """
**PATTERN 1: Bullish Breakout**
Market Logic: When price breaks above previous high, momentum traders enter
Entry Logic: current_close > previous_high
Direction: long
Stop Logic: previous_low
Target Logic: entry_price + (entry_price - stop_price) * 2.0
Position Size: 1.0
Timeframe: 5min

**PATTERN 2: Bearish Breakdown**
Market Logic: When price breaks below previous low, sellers dominate
Entry Logic: current_close < previous_low
Direction: short
Stop Logic: previous_high
Target Logic: entry_price - (stop_price - entry_price) * 1.5
Position Size: 1.0
Timeframe: 5min

**PATTERN 3: Simple Momentum**
Market Logic: Price continuation after strong moves
Entry Logic: current_close > previous_close
Direction: long
Stop Logic: previous_low
Target Logic: entry_price + (entry_price - stop_price) * 1.0
Position Size: 2.0
Timeframe: 5min
"""
    
    try:
        # Test parsing
        parser = BacktestingRuleParser()
        rules = parser.parse_llm_response(sample_response)
        
        print(f"✅ Parsed {len(rules)} rules successfully")
        
        for rule in rules:
            print(f"   📊 Rule {rule.rule_id}: {rule.name}")
            print(f"      Entry: {rule.entry_logic}")
            print(f"      Direction: {rule.direction}")
            print(f"      Stop: {rule.stop_logic}")
            print(f"      Target: {rule.target_logic}")
        
        # Test function generation
        functions = parser.generate_python_functions()
        print(f"✅ Generated {len(functions)} Python functions")
        
        # Test with sample data
        sample_data = pd.DataFrame({
            'Open': [1.1000, 1.1010, 1.1020, 1.1030, 1.1040],
            'High': [1.1005, 1.1015, 1.1025, 1.1035, 1.1045],
            'Low': [0.9995, 1.1005, 1.1015, 1.1025, 1.1035],
            'Close': [1.1002, 1.1012, 1.1022, 1.1032, 1.1042],
            'Volume': [1000, 1100, 1200, 1300, 1400]
        })
        
        signals_found = 0
        for i, func in enumerate(functions):
            for idx in range(2, len(sample_data)):
                signal = func(sample_data, idx)
                if signal:
                    signals_found += 1
                    print(f"   🎯 Function {i+1} Signal: {signal['direction']} at {signal['entry_price']:.4f}")
                    break
        
        print(f"✅ Found {signals_found} signals from {len(functions)} functions")
        
        return len(rules), len(functions), signals_found
        
    except Exception as e:
        print(f"❌ Parser test failed: {e}")
        import traceback
        traceback.print_exc()
        return 0, 0, 0

def test_prompt_generation():
    """Test the new backtesting-only prompt generation"""
    print("\n🧪 Testing Backtesting-Only Prompt Generation...")
    
    # Sample OHLC data
    sample_ohlc = pd.DataFrame({
        'Open': [1.1000, 1.1010, 1.1020],
        'High': [1.1005, 1.1015, 1.1025],
        'Low': [0.9995, 1.1005, 1.1015],
        'Close': [1.1002, 1.1012, 1.1022],
        'Volume': [1000, 1100, 1200]
    })
    
    try:
        prompt = BacktestingOnlyPrompts.generate_backtesting_discovery_prompt(
            ohlc_data=sample_ohlc,
            market_summaries="Sample behavioral analysis",
            performance_feedback="Previous patterns showed 65% win rate"
        )
        
        print(f"✅ Generated prompt ({len(prompt)} characters)")
        
        # Check for key improvements
        improvements = []
        if "MT4 Entry:" not in prompt:
            improvements.append("✅ Removed MT4 syntax requirements")
        if "backticks" not in prompt.lower():
            improvements.append("✅ No backtick formula requirements")
        if "risk-reward ratio" not in prompt.lower():
            improvements.append("✅ No risk-reward ratio text")
        if "Python" in prompt:
            improvements.append("✅ Python-focused instructions")
        if "simple" in prompt.lower():
            improvements.append("✅ Emphasizes simplicity")
        
        for improvement in improvements:
            print(f"   {improvement}")
        
        return len(improvements)
        
    except Exception as e:
        print(f"❌ Prompt generation test failed: {e}")
        return 0

def run_integration_test():
    """Run a quick integration test"""
    print("\n🧪 Running Integration Test...")
    
    try:
        # Test the complete flow
        sample_ohlc = pd.DataFrame({
            'Open': [1.1000, 1.1010, 1.1020, 1.1030, 1.1040],
            'High': [1.1005, 1.1015, 1.1025, 1.1035, 1.1045],
            'Low': [0.9995, 1.1005, 1.1015, 1.1025, 1.1035],
            'Close': [1.1002, 1.1012, 1.1022, 1.1032, 1.1042],
            'Volume': [1000, 1100, 1200, 1300, 1400]
        })
        
        # Generate prompt
        prompt = BacktestingOnlyPrompts.generate_backtesting_discovery_prompt(
            ohlc_data=sample_ohlc,
            market_summaries="Uptrend market with increasing volume",
            performance_feedback=""
        )
        
        print(f"✅ Prompt generated ({len(prompt)} chars)")
        
        # Simulate LLM response (simplified)
        simulated_response = """
**PATTERN 1: Momentum Breakout**
Market Logic: Price breaks above resistance with volume
Entry Logic: current_close > previous_high
Direction: long
Stop Logic: previous_low
Target Logic: entry_price + (entry_price - stop_price) * 2.0
Position Size: 1.0
Timeframe: 5min
"""
        
        # Parse response
        functions = parse_backtesting_rules(simulated_response)
        print(f"✅ Parsed {len(functions)} functions")
        
        # Test execution
        if functions:
            func = functions[0]
            signal = func(sample_ohlc, 3)  # Test on index 3
            if signal:
                print(f"✅ Signal generated: {signal['direction']} at {signal['entry_price']:.4f}")
                return True
            else:
                print("⚠️ No signal generated")
        
        return len(functions) > 0
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Testing Backtesting-Only Approach")
    print("=" * 50)
    
    # Run tests
    rules_parsed, functions_generated, signals_found = test_backtesting_parser()
    prompt_improvements = test_prompt_generation()
    integration_success = run_integration_test()
    
    # Summary
    print("\n📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    print(f"Rules Parsed: {rules_parsed}/3 (target: 3)")
    print(f"Functions Generated: {functions_generated}/3 (target: 3)")
    print(f"Signals Found: {signals_found} (target: >0)")
    print(f"Prompt Improvements: {prompt_improvements}/5 (target: 5)")
    print(f"Integration Test: {'✅ PASSED' if integration_success else '❌ FAILED'}")
    
    # Calculate success rate
    total_tests = 5
    passed_tests = sum([
        rules_parsed == 3,
        functions_generated == 3,
        signals_found > 0,
        prompt_improvements >= 4,
        integration_success
    ])
    
    success_rate = (passed_tests / total_tests) * 100
    print(f"\n🎯 Overall Success Rate: {success_rate:.1f}% (target: >90%)")
    
    if success_rate >= 90:
        print("✅ BACKTESTING-ONLY APPROACH: SUCCESS!")
        print("   Ready to proceed with implementation")
    else:
        print("⚠️ BACKTESTING-ONLY APPROACH: NEEDS IMPROVEMENT")
        print("   Review failed tests before proceeding")
