#!/usr/bin/env python3
"""
Test both LONG and SHORT pattern validation
"""

import sys
sys.path.append('src')

import pandas as pd
import numpy as np
from backtesting import Backtest, Strategy
from config import <PERSON><PERSON>gerConfig
from llm_rule_parser import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_both_directions():
    """Test LONG and SHORT pattern validation"""
    print("🔍 TESTING BOTH LONG AND SHORT PATTERNS")
    print("=" * 60)
    
    config = JaegerConfig()
    
    # Create test data
    dates = pd.date_range('2024-01-01', periods=100, freq='1min')
    test_data = pd.DataFrame({
        'Open': np.random.uniform(8300, 8350, 100),
        'High': np.random.uniform(8320, 8370, 100),
        'Low': np.random.uniform(8280, 8330, 100),
        'Close': np.random.uniform(8300, 8350, 100),
        'Volume': np.random.randint(1000, 5000, 100)
    }, index=dates)
    
    # Test patterns with both directions
    patterns_text = """
**PATTERN 1: Bullish Breakout**
Market Logic: When close breaks above previous high, momentum continues up
MT4 Entry: Close[0] > High[1]
MT4 Direction: OP_BUY
MT4 Stop: Low[1]
MT4 Target: Close[0] + (Close[0] - Low[1]) * 2.0
MT4 Position Size: 1 unit
MT4 Timeframe: PERIOD_M5

**PATTERN 2: Bearish Breakdown**
Market Logic: When close breaks below previous low, momentum continues down
MT4 Entry: Close[0] < Low[1]
MT4 Direction: OP_SELL
MT4 Stop: High[1]
MT4 Target: Close[0] - (High[1] - Close[0]) * 1.5
MT4 Position Size: 1 unit
MT4 Timeframe: PERIOD_M5

**PATTERN 3: Contradictory Pattern (LLM Error)**
Market Logic: This is a contradictory pattern that LLM might generate
MT4 Entry: Close[0] < Low[1]
MT4 Direction: OP_BUY
MT4 Stop: High[1]
MT4 Target: Close[0] - (High[1] - Close[0]) * 1.5
MT4 Position Size: 1 unit
MT4 Timeframe: PERIOD_M5
"""
    
    print(f"📊 Test data: {len(test_data)} bars")
    print(f"📊 Price range: {test_data['Close'].min():.1f} - {test_data['Close'].max():.1f}")
    
    # Parse patterns
    try:
        parser = LLMRuleParser()
        rules = parser.parse_llm_response(patterns_text)
        print(f"✅ Parsed {len(rules)} rules")
        
        # Create rule functions
        rule_functions = []
        for rule in rules:
            rule_func = parser._create_python_function(rule)
            if rule_func:
                rule_functions.append(rule_func)
        
        print(f"✅ Created {len(rule_functions)} rule functions")
        
    except Exception as e:
        print(f"❌ Pattern parsing failed: {e}")
        return
    
    # Test each pattern
    for i, rule_func in enumerate(rule_functions, 1):
        print(f"\n🔍 Testing Pattern {i}...")
        
        # Create strategy with validation
        class TestStrategy(Strategy):
            def init(self):
                self.rule_functions = [rule_func]
                self.full_ohlc_data = test_data.copy()
                self.signal_count = 0
                self.validation_passes = 0
                self.validation_failures = 0
                self.direction_corrections = 0
                
            def next(self):
                current_idx = len(self.data.Close) - 1
                if current_idx < 2:
                    return
                    
                try:
                    signal = rule_func(self.full_ohlc_data, current_idx)
                    
                    if signal:
                        self.signal_count += 1
                        
                        # Test validation with direction detection
                        entry = signal.get('entry_price')
                        sl = signal.get('stop_loss')
                        tp = signal.get('take_profit')
                        direction = signal.get('direction', 'long')
                        
                        # Smart direction detection (like Cortex does)
                        actual_direction = 'long' if sl < entry else 'short'
                        
                        if direction != actual_direction:
                            self.direction_corrections += 1
                            if self.direction_corrections <= 3:
                                print(f"      🔧 Pattern {i} - Direction correction #{self.direction_corrections}:")
                                print(f"         Stated: {direction.upper()}, Actual: {actual_direction.upper()}")
                                print(f"         Entry: {entry:.4f}, Stop: {sl:.4f}")
                            direction = actual_direction
                        
                        # Validate
                        if self._validate_pattern(direction, entry, sl, tp):
                            self.validation_passes += 1
                            # Try to place order
                            try:
                                if direction == 'long':
                                    self.buy(size=1.0)
                                else:
                                    self.sell(size=1.0)
                            except Exception as e:
                                if self.validation_passes <= 3:
                                    print(f"      ⚠️ Order placement failed: {e}")
                        else:
                            self.validation_failures += 1
                            
                except Exception as e:
                    if not hasattr(self, 'rule_errors'):
                        self.rule_errors = 0
                    self.rule_errors += 1
                    if self.rule_errors <= 3:
                        print(f"      ❌ Rule evaluation error: {e}")
            
            def _validate_pattern(self, direction, entry, sl, tp):
                """Validate pattern logic"""
                if not all([entry, sl, tp]):
                    return False
                    
                if direction == 'long':
                    return sl < entry and tp > entry
                else:  # short
                    return sl > entry and tp < entry
        
        # Run backtest
        try:
            bt = Backtest(
                test_data,
                TestStrategy,
                cash=config.DEFAULT_INITIAL_CASH,
                spread=config.DEFAULT_SPREAD,
                commission=config.DEFAULT_COMMISSION,
                margin=config.DEFAULT_MARGIN,
                exclusive_orders=True
            )
            
            stats = bt.run()
            strategy = stats._strategy
            
            print(f"   📊 Pattern {i} Results:")
            print(f"      Signals generated: {strategy.signal_count}")
            print(f"      Direction corrections: {strategy.direction_corrections}")
            print(f"      Validation passes: {strategy.validation_passes}")
            print(f"      Validation failures: {strategy.validation_failures}")
            print(f"      Trades executed: {stats['# Trades']}")
            
            if strategy.direction_corrections > 0:
                print(f"      ✅ Smart direction detection working!")
            
            if stats['# Trades'] > 0:
                print(f"      ✅ Pattern {i} successfully executed trades!")
                print(f"      📈 Return: {stats['Return [%]']:.2f}%")
            else:
                print(f"      ❌ Pattern {i} generated signals but no trades")
                
        except Exception as e:
            print(f"   ❌ Backtest failed: {e}")

if __name__ == "__main__":
    test_both_directions()
