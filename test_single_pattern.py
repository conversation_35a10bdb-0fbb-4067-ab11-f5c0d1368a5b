#!/usr/bin/env python3
"""
Test a single pattern with the exact Cortex backtesting integration
"""

import sys
import os
sys.path.append('src')

import pandas as pd
from data_ingestion import DataIngestionManager
from llm_rule_parser import parse_llm_rule
from backtesting import Backtest, Strategy
from config import config

def test_single_pattern():
    """Test a single pattern with the exact Cortex integration"""
    
    print("🔍 TESTING SINGLE PATTERN WITH CORTEX INTEGRATION")
    print("=" * 60)
    
    # 1. Load data
    data_file = "data/2025.6.23GBRIDXGBP_M1_UTCPlus01-M1-No Session.csv"
    data_manager = DataIngestionManager()
    ohlc_data = data_manager.load_market_data(data_file)
    ohlc_data['hour'] = ohlc_data.index.hour
    
    # Use FULL dataset like Cortex does
    print(f"📊 Using FULL dataset: {len(ohlc_data)} bars for testing (same as Cortex)")
    
    # 2. Test the EXACT Pattern 1 that <PERSON>rtex generated
    pattern_text = """**Pattern 1: Bullish Breakout**

Market Logic: When the current close is above the previous high, it's likely that a bullish breakout has occurred. We'll use this logic to enter long positions.

MT4 Entry: Close[0] > High[1]
MT4 Direction: OP_BUY
MT4 Stop: Low[1]
MT4 Target: Close[0] + (Close[0] - Low[1]) * 2.0
MT4 Timeframe: PERIOD_M5"""

    print(f"\n🧠 Testing Pattern 1...")
    
    # 3. Parse rule
    rule_functions = parse_llm_rule(pattern_text, {})
    if not rule_functions:
        print("❌ No rule functions generated!")
        return
    
    rule_func = rule_functions[0]
    print(f"✅ Rule function generated")
    
    # 4. Create Strategy class exactly like Cortex
    class PatternStrategy(Strategy):
        def init(self):
            self.rule_functions = [rule_func]
            self.full_ohlc_data = ohlc_data.copy()
            self.pattern_text = pattern_text

        def next(self):
            current_idx = len(self.data.Close) - 1
            if current_idx < 2:
                return

            # DEBUG: Add detailed logging
            if current_idx % 1000 == 0:
                print(f"      🔍 Testing bar {current_idx}/{len(self.full_ohlc_data)}")
            
            try:
                signal = rule_func(self.full_ohlc_data, current_idx)
                
                if signal:
                    print(f"      🎯 SIGNAL FOUND at bar {current_idx}: {signal}")
                    
                    # Use position size from signal (already fixed by our position sizing solution)
                    position_size = signal.get('position_size', 1.0)
                    
                    sl_price = signal.get('stop_loss')
                    tp_price = signal.get('take_profit')
                    
                    if signal['direction'] == 'long':
                        self.buy(size=position_size, sl=sl_price, tp=tp_price)
                        print(f"      🎯 LONG order placed: size={position_size}, sl={sl_price}, tp={tp_price}")
                    elif signal['direction'] == 'short':
                        self.sell(size=position_size, sl=sl_price, tp=tp_price)
                        print(f"      🎯 SHORT order placed: size={position_size}, sl={sl_price}, tp={tp_price}")
                elif current_idx < 10:
                    print(f"      🔍 No signal at bar {current_idx}")
            except Exception as e:
                if current_idx < 10:
                    print(f"      ⚠️ Rule evaluation error at bar {current_idx}: {e}")
    
    # 5. Run backtest with exact Cortex settings
    print(f"\n🔄 Running backtest...")
    bt = Backtest(
        ohlc_data,
        PatternStrategy,
        cash=config.DEFAULT_INITIAL_CASH,
        spread=config.DEFAULT_SPREAD,
        commission=config.DEFAULT_COMMISSION,
        margin=config.DEFAULT_MARGIN,
        exclusive_orders=config.DEFAULT_EXCLUSIVE_ORDERS,
        finalize_trades=config.DEFAULT_FINALIZE_TRADES
    )
    
    stats = bt.run()
    
    trade_count = stats.get('# Trades', 0)
    return_pct = stats.get('Return [%]', 0)
    
    print(f"\n🎯 BACKTEST RESULTS:")
    print(f"   📊 Trades executed: {trade_count}")
    print(f"   📈 Return: {return_pct:.2f}%")
    
    if trade_count > 0:
        print(f"✅ SUCCESS! Pattern 1 executed {trade_count} trades!")
        print(f"📊 Full stats:")
        for key, value in stats.items():
            if isinstance(value, (int, float)):
                print(f"   {key}: {value}")
    else:
        print(f"❌ ZERO TRADES - Need to investigate further")

if __name__ == "__main__":
    test_single_pattern()
