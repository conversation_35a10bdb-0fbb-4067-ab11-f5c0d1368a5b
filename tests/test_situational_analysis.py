#!/usr/bin/env python3
"""
Test script to verify the SITUATIONAL ANALYSIS transformation is working correctly
Tests the new situational analysis components without requiring LM Studio
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src directory to path (from tests directory)
import os
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'src'))

from ai_integration.legacy_situational_prompts import SituationalAnalysisPrompts

def create_test_data():
    """Load real DAX market data for testing - NO SYNTHETIC DATA"""
    print("📊 Loading real DAX market data...")
    import os

    # Load real DAX data
    data_file = os.path.join('tests', 'RealTestData', 'dax_1000_bars.csv')

    if not os.path.exists(data_file):
        raise FileNotFoundError(f"Real test data not found: {data_file}")

    # Load the CSV data
    data = pd.read_csv(data_file)

    # Handle different datetime formats
    if 'DateTime' in data.columns:
        data['datetime'] = pd.to_datetime(data['DateTime'])
    elif 'Date' in data.columns and 'Time' in data.columns:
        # Convert to proper datetime format (old format)
        data['datetime'] = pd.to_datetime(data['Date'].astype(str) + ' ' + data['Time'].astype(str))
    else:
        raise ValueError("No datetime columns found in test data")

    # Standardize column names to lowercase
    data = data.rename(columns={
        'Open': 'open',
        'High': 'high',
        'Low': 'low',
        'Close': 'close',
        'Volume': 'volume'
    })

    # Add hour column
    data['hour'] = data['datetime'].dt.hour

    print(f"✅ Loaded real test data: {len(data)} records")
    print(f"   Price range: {data['Close'].min():.5f} to {data['Close'].max():.5f}")
    print(f"   Date range: {data['datetime'].min()} to {data['datetime'].max()}")

    return data

def test_situational_prompts():
    """Test the situational analysis prompts"""
    print("\n🧠 Testing Situational Analysis Prompts...")
    
    # Test core questions
    core_questions = SituationalAnalysisPrompts.get_core_situational_questions()
    print(f"✅ Core situational questions: {len(core_questions)}")
    for i, question in enumerate(core_questions[:3], 1):
        print(f"   {i}. {question}")
    
    # Test Tom Hougaard examples
    tom_examples = SituationalAnalysisPrompts.get_tom_hougaard_examples()
    print(f"✅ Tom Hougaard examples: {len(tom_examples)}")
    for i, example in enumerate(tom_examples[:3], 1):
        print(f"   {i}. {example}")
    
    # Test situational categories
    categories = SituationalAnalysisPrompts.get_situational_categories()
    print(f"✅ Situational categories: {len(categories)}")
    for category, info in categories.items():
        print(f"   - {category}: {info['description']}")
    
    assert len(core_questions) > 0
    assert len(tom_examples) > 0
    assert len(categories) > 0

def test_situational_context_analyzer():
    """Test removed - SituationalContextAnalyzer eliminated in favor of pure LLM analysis"""
    print("\n🔍 SituationalContextAnalyzer removed - LLM now does all situational analysis")
    print("✅ This is the correct behavior - no mechanical pre-analysis needed")
    print("✅ LLM will discover situational patterns directly from OHLC data")
    # Test passes by design - this is the correct behavior

def test_situational_prompt_generation():
    """Test the situational prompt generation"""
    print("\n📝 Testing Situational Prompt Generation...")
    
    # Create test data
    test_data = create_test_data()
    
    # Generate situational analysis prompt
    prompt = SituationalAnalysisPrompts.generate_situational_discovery_prompt(
        ohlc_data=test_data,
        profit_context="Test profitable conditions identified",
        market_summaries="Test market summaries"
    )
    
    print("✅ Situational analysis prompt generated")
    print(f"   Prompt length: {len(prompt)} characters")
    
    # Show key sections of the prompt
    lines = prompt.split('\n')
    print("\n📖 Key prompt sections:")
    
    for line in lines:
        if any(keyword in line.upper() for keyword in ['SITUATIONAL ANALYSIS', 'CORE SITUATIONAL', 'MARKET SITUATION:', 'PARTICIPANT BEHAVIOR:']):
            print(f"   {line}")
    
    # Verify it contains both situational analysis and breakout execution elements
    situational_keywords = [
        'SITUATIONAL ANALYSIS',
        'participant behavior',
        'market situation',
        'behavioral patterns',
        'BREAKOUT EXECUTION'
    ]
    
    found_keywords = []
    for keyword in situational_keywords:
        if keyword.lower() in prompt.lower():
            found_keywords.append(keyword)
    
    print(f"✅ Situational analysis keywords found: {len(found_keywords)}/{len(situational_keywords)}")
    for keyword in found_keywords:
        print(f"   ✓ {keyword}")
    
    assert len(found_keywords) == len(situational_keywords)

def main():
    """Main test function"""
    print("🧠 SITUATIONAL ANALYSIS TRANSFORMATION TEST")
    print("=" * 60)
    print("Testing the implementation of SITUATIONAL ANALYSIS methodology")
    print("Focus: Market situations and participant behavior analysis")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 3
    
    try:
        # Test 1: Situational Prompts
        if test_situational_prompts():
            tests_passed += 1
            print("✅ Test 1 PASSED: Situational Prompts")
        else:
            print("❌ Test 1 FAILED: Situational Prompts")
    except Exception as e:
        print(f"❌ Test 1 ERROR: {e}")
    
    try:
        # Test 2: Situational Context Analyzer (now removed)
        result = test_situational_context_analyzer()
        if result:
            tests_passed += 1
            print("✅ Test 2 PASSED: Mechanical Analysis Removed")
        else:
            print("❌ Test 2 FAILED: Mechanical Analysis Removal")
    except Exception as e:
        print(f"❌ Test 2 ERROR: {e}")
    
    try:
        # Test 3: Situational Prompt Generation
        if test_situational_prompt_generation():
            tests_passed += 1
            print("✅ Test 3 PASSED: Situational Prompt Generation")
        else:
            print("❌ Test 3 FAILED: Situational Prompt Generation")
    except Exception as e:
        print(f"❌ Test 3 ERROR: {e}")
    
    # Final results
    print("\n" + "=" * 60)
    print(f"🎯 SITUATIONAL ANALYSIS TRANSFORMATION TEST RESULTS")
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 ALL TESTS PASSED - SITUATIONAL ANALYSIS TRANSFORMATION SUCCESSFUL!")
        print("✅ The system has been successfully transformed to use SITUATIONAL ANALYSIS methodology")
        print("✅ Focus: Market situations, participant behavior, statistical behavioral edges")
        print("❌ NOT: Chart patterns, technical indicators, fundamental analysis")
    else:
        print("⚠️  SOME TESTS FAILED - Review implementation")
    
    print("=" * 60)

def run_situational_analysis_tests():
    """Run situational analysis tests for the comprehensive test runner"""
    print("🧪 SITUATIONAL ANALYSIS TESTS")
    print("=" * 50)

    tests_passed = 0
    total_tests = 3

    try:
        # Test 1: Situational Prompts
        if test_situational_prompts():
            tests_passed += 1
        else:
            print("❌ Test 1 FAILED: Situational Prompts")
    except Exception as e:
        print(f"❌ Test 1 ERROR: {e}")

    try:
        # Test 2: Situational Context Analyzer (now removed)
        result = test_situational_context_analyzer()
        if result:
            tests_passed += 1
        else:
            print("❌ Test 2 FAILED: Mechanical Analysis Removal")
    except Exception as e:
        print(f"❌ Test 2 ERROR: {e}")

    try:
        # Test 3: Situational Prompt Generation
        if test_situational_prompt_generation():
            tests_passed += 1
        else:
            print("❌ Test 3 FAILED: Situational Prompt Generation")
    except Exception as e:
        print(f"❌ Test 3 ERROR: {e}")

    # Print summary
    print("\n" + "=" * 50)
    print(f"🎯 SITUATIONAL ANALYSIS TEST RESULTS")
    print(f"Tests run: {total_tests}")
    print(f"Failures: {total_tests - tests_passed}")
    print(f"Errors: 0")

    if tests_passed == total_tests:
        print("✅ ALL SITUATIONAL ANALYSIS TESTS PASSED!")
    else:
        print("❌ Some situational analysis tests failed")

    return tests_passed == total_tests

if __name__ == "__main__":
    main()
